/**
 * CozyWish Design System
 * 
 * Main entry point for the CozyWish design system.
 * Import this single file to get the complete design system.
 * 
 * Usage:
 * <link rel="stylesheet" href="path/to/cozywish.css">
 * 
 * Or in CSS:
 * @import 'path/to/cozywish.css';
 * 
 * Version: 1.0.0
 * Author: CozyWish Team
 * License: MIT
 */

/* ===== CORE FOUNDATION ===== */
/* Import design tokens first - these are the foundation */
@import 'css/tokens.css';

/* Import Bootstrap customizations - these override Bootstrap defaults */
@import 'css/custom.css';

/* ===== COMPONENT LIBRARY ===== */
/* Import component-specific styles in logical order */

/* Base Components */
@import 'css/forms.css';           /* Form controls and inputs */
@import 'css/alerts.css';          /* Alert messages and notifications */
@import 'css/badges.css';          /* Badges and labels */
@import 'css/card.css';            /* Cards and containers */
@import 'css/tables.css';          /* Tables and data display */

/* Navigation Components */
@import 'css/navbar.css';          /* Main navigation bar */
@import 'css/navigation.css';      /* Secondary navigation and menus */

/* Interactive Components */
@import 'css/feedback.css';        /* User feedback components */
@import 'css/loading.css';         /* Loading states and spinners */

/* Specialized Components */
@import 'css/booking-calendar.css'; /* Calendar and booking functionality */

/* ===== DESIGN SYSTEM UTILITIES ===== */

/* Additional utility classes for the design system */
:root {
  /* Component spacing helpers */
  --cw-component-margin: var(--cw-spacing-4);
  --cw-component-padding: var(--cw-spacing-4);
  --cw-section-spacing: var(--cw-spacing-8);
  
  /* Layout helpers */
  --cw-container-padding: var(--cw-spacing-4);
  --cw-grid-gap: var(--cw-spacing-4);
  
  /* Animation helpers */
  --cw-hover-transform: translateY(-2px);
  --cw-active-transform: translateY(0);
}

/* ===== GLOBAL BASE STYLES ===== */

/* Ensure consistent box-sizing */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Base body styles */
body {
  margin: 0;
  font-family: var(--cw-font-family-primary);
  font-size: var(--cw-font-size-base);
  font-weight: var(--cw-font-weight-normal);
  line-height: var(--cw-line-height-relaxed);
  color: var(--cw-color-text-primary);
  background-color: var(--cw-color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Enhanced focus styles for better accessibility */
:focus-visible {
  outline: 2px solid var(--cw-color-primary);
  outline-offset: 2px;
}

/* ===== LAYOUT UTILITIES ===== */

/* Container utilities */
.cw-container {
  width: 100%;
  padding-left: var(--cw-container-padding);
  padding-right: var(--cw-container-padding);
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 576px) {
  .cw-container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .cw-container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .cw-container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .cw-container {
    max-width: 1140px;
  }
}

@media (min-width: 1400px) {
  .cw-container {
    max-width: 1320px;
  }
}

/* Section spacing utilities */
.cw-section {
  padding: var(--cw-section-spacing) 0;
}

.cw-section-sm {
  padding: var(--cw-spacing-6) 0;
}

.cw-section-lg {
  padding: var(--cw-spacing-12) 0;
}

/* Grid utilities */
.cw-grid {
  display: grid;
  gap: var(--cw-grid-gap);
}

.cw-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.cw-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.cw-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Responsive grid */
@media (max-width: 768px) {
  .cw-grid-2,
  .cw-grid-3,
  .cw-grid-4 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .cw-grid-3,
  .cw-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Flexbox utilities */
.cw-flex {
  display: flex;
}

.cw-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cw-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cw-flex-column {
  display: flex;
  flex-direction: column;
}

.cw-flex-wrap {
  flex-wrap: wrap;
}

.cw-flex-grow {
  flex-grow: 1;
}

/* ===== TYPOGRAPHY UTILITIES ===== */

/* Heading utilities */
.cw-heading-1 {
  font-size: var(--cw-font-size-5xl);
  font-weight: var(--cw-font-weight-bold);
  line-height: var(--cw-line-height-tight);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-6);
}

.cw-heading-2 {
  font-size: var(--cw-font-size-4xl);
  font-weight: var(--cw-font-weight-bold);
  line-height: var(--cw-line-height-tight);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-5);
}

.cw-heading-3 {
  font-size: var(--cw-font-size-3xl);
  font-weight: var(--cw-font-weight-semibold);
  line-height: var(--cw-line-height-snug);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-4);
}

.cw-heading-4 {
  font-size: var(--cw-font-size-2xl);
  font-weight: var(--cw-font-weight-semibold);
  line-height: var(--cw-line-height-snug);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-3);
}

.cw-heading-5 {
  font-size: var(--cw-font-size-xl);
  font-weight: var(--cw-font-weight-medium);
  line-height: var(--cw-line-height-normal);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-3);
}

.cw-heading-6 {
  font-size: var(--cw-font-size-lg);
  font-weight: var(--cw-font-weight-medium);
  line-height: var(--cw-line-height-normal);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-2);
}

/* Text utilities */
.cw-text-lead {
  font-size: var(--cw-font-size-lg);
  font-weight: var(--cw-font-weight-normal);
  line-height: var(--cw-line-height-relaxed);
  color: var(--cw-color-text-secondary);
}

.cw-text-body {
  font-size: var(--cw-font-size-base);
  font-weight: var(--cw-font-weight-normal);
  line-height: var(--cw-line-height-relaxed);
  color: var(--cw-color-text-primary);
  margin-bottom: var(--cw-spacing-4);
}

.cw-text-small {
  font-size: var(--cw-font-size-sm);
  font-weight: var(--cw-font-weight-normal);
  line-height: var(--cw-line-height-normal);
  color: var(--cw-color-text-secondary);
}

.cw-text-xs {
  font-size: var(--cw-font-size-xs);
  font-weight: var(--cw-font-weight-normal);
  line-height: var(--cw-line-height-normal);
  color: var(--cw-color-text-muted);
}

/* ===== ICON UTILITIES ===== */

/* Bootstrap Icons Global Styling */
.bi {
  font-size: var(--cw-font-size-md); /* 18px - slightly bigger than default 16px */
  font-weight: var(--cw-font-weight-bold); /* Make icons bold */
  font-variation-settings: "wght" var(--cw-font-weight-bold); /* For variable font weight support */
  line-height: 1;
  vertical-align: text-bottom;
}

/* Icon size variants */
.bi-xs {
  font-size: var(--cw-font-size-sm); /* 14px */
}

.bi-sm {
  font-size: var(--cw-font-size-base); /* 16px */
}

.bi-lg {
  font-size: var(--cw-font-size-lg); /* 20px */
}

.bi-xl {
  font-size: var(--cw-font-size-xl); /* 24px */
}

.bi-2xl {
  font-size: var(--cw-font-size-2xl); /* 30px */
}

/* Icon weight variants for fine control */
.bi-light {
  font-weight: var(--cw-font-weight-light);
  font-variation-settings: "wght" var(--cw-font-weight-light);
}

.bi-normal {
  font-weight: var(--cw-font-weight-normal);
  font-variation-settings: "wght" var(--cw-font-weight-normal);
}

.bi-medium {
  font-weight: var(--cw-font-weight-medium);
  font-variation-settings: "wght" var(--cw-font-weight-medium);
}

.bi-semibold {
  font-weight: var(--cw-font-weight-semibold);
  font-variation-settings: "wght" var(--cw-font-weight-semibold);
}

.bi-bold {
  font-weight: var(--cw-font-weight-bold);
  font-variation-settings: "wght" var(--cw-font-weight-bold);
}

/* Icon spacing helpers */
.bi-me-1 { margin-right: var(--cw-spacing-1); }
.bi-me-2 { margin-right: var(--cw-spacing-2); }
.bi-me-3 { margin-right: var(--cw-spacing-3); }
.bi-ms-1 { margin-left: var(--cw-spacing-1); }
.bi-ms-2 { margin-left: var(--cw-spacing-2); }
.bi-ms-3 { margin-left: var(--cw-spacing-3); }

/* ===== SPACING UTILITIES ===== */

/* Margin utilities */
.cw-m-0 { margin: 0; }
.cw-m-1 { margin: var(--cw-spacing-1); }
.cw-m-2 { margin: var(--cw-spacing-2); }
.cw-m-3 { margin: var(--cw-spacing-3); }
.cw-m-4 { margin: var(--cw-spacing-4); }
.cw-m-5 { margin: var(--cw-spacing-5); }
.cw-m-6 { margin: var(--cw-spacing-6); }
.cw-m-8 { margin: var(--cw-spacing-8); }

/* Margin top/bottom */
.cw-mt-0 { margin-top: 0; }
.cw-mt-1 { margin-top: var(--cw-spacing-1); }
.cw-mt-2 { margin-top: var(--cw-spacing-2); }
.cw-mt-3 { margin-top: var(--cw-spacing-3); }
.cw-mt-4 { margin-top: var(--cw-spacing-4); }
.cw-mt-6 { margin-top: var(--cw-spacing-6); }
.cw-mt-8 { margin-top: var(--cw-spacing-8); }

.cw-mb-0 { margin-bottom: 0; }
.cw-mb-1 { margin-bottom: var(--cw-spacing-1); }
.cw-mb-2 { margin-bottom: var(--cw-spacing-2); }
.cw-mb-3 { margin-bottom: var(--cw-spacing-3); }
.cw-mb-4 { margin-bottom: var(--cw-spacing-4); }
.cw-mb-6 { margin-bottom: var(--cw-spacing-6); }
.cw-mb-8 { margin-bottom: var(--cw-spacing-8); }

/* Padding utilities */
.cw-p-0 { padding: 0; }
.cw-p-1 { padding: var(--cw-spacing-1); }
.cw-p-2 { padding: var(--cw-spacing-2); }
.cw-p-3 { padding: var(--cw-spacing-3); }
.cw-p-4 { padding: var(--cw-spacing-4); }
.cw-p-5 { padding: var(--cw-spacing-5); }
.cw-p-6 { padding: var(--cw-spacing-6); }
.cw-p-8 { padding: var(--cw-spacing-8); }

/* ===== COLOR UTILITIES ===== */

/* Text colors */
.cw-text-primary { color: var(--cw-color-primary); }
.cw-text-secondary { color: var(--cw-color-secondary); }
.cw-text-accent { color: var(--cw-color-accent); }
.cw-text-success { color: var(--cw-color-success); }
.cw-text-warning { color: var(--cw-color-warning); }
.cw-text-danger { color: var(--cw-color-danger); }
.cw-text-info { color: var(--cw-color-info); }
.cw-text-muted { color: var(--cw-color-text-muted); }
.cw-text-white { color: var(--cw-color-white); }

/* Background colors */
.cw-bg-primary { 
  background-color: var(--cw-color-primary); 
  color: var(--cw-color-white);
}
.cw-bg-secondary { 
  background-color: var(--cw-color-secondary); 
  color: var(--cw-color-white);
}
.cw-bg-accent { 
  background-color: var(--cw-color-accent); 
  color: var(--cw-color-text-primary);
}
.cw-bg-success { 
  background-color: var(--cw-color-success); 
  color: var(--cw-color-white);
}
.cw-bg-warning { 
  background-color: var(--cw-color-warning); 
  color: var(--cw-color-white);
}
.cw-bg-danger { 
  background-color: var(--cw-color-danger); 
  color: var(--cw-color-white);
}
.cw-bg-info { 
  background-color: var(--cw-color-info); 
  color: var(--cw-color-white);
}
.cw-bg-white { 
  background-color: var(--cw-color-white); 
  color: var(--cw-color-text-primary);
}
.cw-bg-gray-50 { 
  background-color: var(--cw-color-gray-50); 
  color: var(--cw-color-text-primary);
}

/* ===== SHADOW UTILITIES ===== */

.cw-shadow-none { box-shadow: var(--cw-shadow-none); }
.cw-shadow-xs { box-shadow: var(--cw-shadow-xs); }
.cw-shadow-sm { box-shadow: var(--cw-shadow-sm); }
.cw-shadow { box-shadow: var(--cw-shadow-base); }
.cw-shadow-md { box-shadow: var(--cw-shadow-md); }
.cw-shadow-lg { box-shadow: var(--cw-shadow-lg); }
.cw-shadow-xl { box-shadow: var(--cw-shadow-xl); }
.cw-shadow-2xl { box-shadow: var(--cw-shadow-2xl); }

/* ===== BORDER UTILITIES ===== */

.cw-border { border: 1px solid var(--cw-color-border); }
.cw-border-0 { border: 0; }
.cw-border-primary { border-color: var(--cw-color-primary); }
.cw-border-secondary { border-color: var(--cw-color-secondary); }

/* Border radius */
.cw-rounded-none { border-radius: var(--cw-radius-none); }
.cw-rounded-sm { border-radius: var(--cw-radius-sm); }
.cw-rounded { border-radius: var(--cw-radius-base); }
.cw-rounded-md { border-radius: var(--cw-radius-md); }
.cw-rounded-lg { border-radius: var(--cw-radius-lg); }
.cw-rounded-xl { border-radius: var(--cw-radius-xl); }
.cw-rounded-2xl { border-radius: var(--cw-radius-2xl); }
.cw-rounded-full { border-radius: var(--cw-radius-full); }

/* ===== ANIMATION UTILITIES ===== */

.cw-transition {
  transition: var(--cw-transition-all);
}

.cw-transition-colors {
  transition: var(--cw-transition-colors);
}

.cw-transition-transform {
  transition: var(--cw-transition-transform);
}

.cw-hover-lift:hover {
  transform: var(--cw-hover-transform);
  box-shadow: var(--cw-shadow-lg);
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Hide/show utilities */
.cw-hidden { display: none; }
.cw-visible { display: block; }

@media (max-width: 767px) {
  .cw-hidden-mobile { display: none; }
  .cw-visible-mobile { display: block; }
}

@media (min-width: 768px) {
  .cw-hidden-desktop { display: none; }
  .cw-visible-desktop { display: block; }
}

/* ===== ACCESSIBILITY UTILITIES ===== */

/* Screen reader only content */
.cw-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link */
.cw-skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--cw-color-primary);
  color: var(--cw-color-white);
  padding: var(--cw-spacing-2) var(--cw-spacing-4);
  border-radius: var(--cw-radius-md);
  text-decoration: none;
  z-index: 1000;
  transition: var(--cw-transition-all);
}

.cw-skip-link:focus {
  top: 6px;
}

/* ===== PRINT STYLES ===== */

@media print {
  .cw-no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
  }
  
  .cw-shadow,
  .cw-shadow-sm,
  .cw-shadow-md,
  .cw-shadow-lg,
  .cw-shadow-xl,
  .cw-shadow-2xl {
    box-shadow: none !important;
  }
}
