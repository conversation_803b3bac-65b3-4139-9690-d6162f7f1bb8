/* Form Components CSS - CozyWish Design System */

/* Form-specific custom styles */
.form-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(67, 37, 27, 0.1);
}

.form-example {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #E0DFDE;
    border-radius: 8px;
    background: #fff;
}

.form-example h5 {
    color: var(--bs-primary);
    margin-bottom: 1rem;
    font-weight: 600;
    background: var(--bs-light);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin: -1.5rem -1.5rem 1rem -1.5rem;
}

.form-example h5 {
    color: var(--bs-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.input-group-text {
    background-color: var(--bs-light);
    border-color: var(--bs-border-color);
    color: var(--bs-primary);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.form-check-input:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25);
}

.password-toggle {
    cursor: pointer;
    color: var(--bs-primary);
    border: 1px solid var(--bs-border-color);
    background: #fff;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    transition: all 0.2s ease-in-out;
    border-left: none;
}

.password-toggle:hover {
    color: var(--bs-primary);
    background-color: #f8f9fa;
    border-color: var(--bs-primary);
    transform: translateY(-1px);
}

.password-toggle:active {
    transform: translateY(0);
    background-color: #e9ecef;
}

.password-toggle:focus {
    color: var(--bs-primary);
    background-color: #f8f9fa;
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25);
    z-index: 3;
    outline: none;
}

.password-toggle i {
    font-size: 0.875rem;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;
    transition: all 0.2s ease-in-out;
}

.password-toggle:hover i {
    color: var(--bs-primary);
    transform: scale(1.1);
}

.password-toggle:active i {
    transform: scale(1);
}

.input-group .password-toggle {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.custom-select-wrapper {
    position: relative;
}

.custom-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2343251B' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.search-input {
    position: relative;
}

.search-input .form-control {
    padding-left: 2.5rem;
}

.search-input .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--bs-primary);
    z-index: 3;
}

.multi-select-container {
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    min-height: 38px;
    background: #fff;
}

.multi-select-container:focus-within {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25);
}

.multi-select-tag {
    display: inline-block;
    background: var(--bs-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin: 0.125rem;
    font-size: 0.875rem;
}

.multi-select-tag .remove-tag {
    margin-left: 0.5rem;
    cursor: pointer;
    font-weight: bold;
}

.multi-select-tag .remove-tag:hover {
    opacity: 0.8;
}

.multi-select-input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 60px;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    color: var(--bs-primary);
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25);
}

.form-floating > label {
    color: #6c757d;
}

.form-floating {
    position: relative;
    margin-bottom: 1rem;
}

.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > textarea.form-control {
    height: auto;
    min-height: calc(3.5rem + 2px);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.input-group .form-control:focus {
    z-index: 3;
}

.input-group .btn:focus {
    z-index: 3;
}

.form-control.is-valid {
    border-color: var(--bs-success);
}

.form-control.is-invalid {
    border-color: var(--bs-danger);
}

.valid-feedback {
    color: var(--bs-success);
}

.invalid-feedback {
    color: var(--bs-danger);
}

.form-range::-webkit-slider-thumb {
    background-color: var(--bs-primary);
}

.form-range::-moz-range-thumb {
    background-color: var(--bs-primary);
}

.form-range::-ms-thumb {
    background-color: var(--bs-primary);
}

.form-range::-webkit-slider-track {
    background-color: var(--bs-border-color);
}

.form-range::-moz-range-track {
    background-color: var(--bs-border-color);
}

.form-range::-ms-track {
    background-color: var(--bs-border-color);
} 

/* Auto-resize textarea */
.auto-resize {
    resize: none;
    overflow: hidden;
    min-height: 100px;
}

/* Character counter */
.form-text .text-danger {
    color: #dc3545 !important;
}

.form-text .text-warning {
    color: #ffc107 !important;
}

/* Custom styled checkboxes and radios */
.custom-checkbox .form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.custom-radio .form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.custom-checkbox .form-check-label i,
.custom-radio .form-check-label i {
    margin-right: 0.5rem;
    color: var(--bs-primary);
}

/* File upload styles */
.custom-file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.custom-file-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    border: 2px dashed var(--bs-border-color);
    border-radius: 0.375rem;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    min-height: 120px;
    flex-direction: column;
    gap: 0.5rem;
}

.custom-file-label:hover {
    border-color: var(--bs-primary);
    background: rgba(67, 37, 27, 0.05);
}

.custom-file-label i {
    font-size: 2rem;
    color: var(--bs-primary);
}

.custom-file-label span {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Drag and drop zone */
.drag-drop-zone {
    border: 2px dashed var(--bs-border-color);
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.drag-drop-zone.drag-over {
    border-color: var(--bs-primary);
    background: rgba(67, 37, 27, 0.05);
}

.drag-drop-zone:hover {
    border-color: var(--bs-primary);
    background: rgba(67, 37, 27, 0.05);
}

.drag-drop-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.drag-drop-icon {
    font-size: 2.5rem;
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
}

.drag-drop-zone h6 {
    color: var(--bs-primary);
    margin: 0;
    font-weight: 600;
}

.drag-drop-zone p {
    margin: 0;
    color: #6c757d;
}

.image-drop-zone {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Image preview styles */
#singleImagePreview img,
#multipleImagePreviewContainer img {
    border: 2px solid var(--bs-border-color);
    transition: all 0.2s ease-in-out;
}

#singleImagePreview img:hover,
#multipleImagePreviewContainer img:hover {
    border-color: var(--bs-primary);
    transform: scale(1.05);
}

/* Progress bar customization */
.progress {
    background-color: #e9ecef;
    border-radius: 0.375rem;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--bs-primary);
    transition: width 0.3s ease-in-out;
}

/* Form validation enhancements */
.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--bs-success);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 3.03-3.03L8 4.77 6.27 6.5 5.3 7.47 2.3 4.47z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--bs-danger);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Validation feedback icons */
.valid-feedback::before,
.invalid-feedback::before {
    margin-right: 0.25rem;
}

.valid-feedback::before {
    content: "✓";
    color: var(--bs-success);
}

.invalid-feedback::before {
    content: "✗";
    color: var(--bs-danger);
}

/* Form group enhancements */
.form-group {
    margin-bottom: 1rem;
}

.form-group .input-group {
    flex-wrap: nowrap;
}

.form-group .input-group-text {
    background-color: var(--bs-light);
    border-color: var(--bs-border-color);
    color: var(--bs-primary);
    font-weight: 500;
}

/* Responsive form adjustments */
@media (max-width: 768px) {
    .drag-drop-zone {
        padding: 1rem;
        min-height: 120px;
    }
    
    .drag-drop-icon {
        font-size: 2rem;
    }
    
    .custom-file-label {
        min-height: 100px;
        padding: 0.75rem;
    }
    
    .form-example {
        padding: 1rem;
    }
    
    .form-section {
        padding: 1rem;
    }
}

/* Password strength indicator */
#passwordStrength {
    font-weight: 600;
    font-size: 0.875rem;
}

/* File upload alerts */
.alert-info {
    background-color: rgba(67, 37, 27, 0.1);
    border-color: rgba(67, 37, 27, 0.2);
    color: var(--bs-primary);
}

.alert-info i {
    color: var(--bs-primary);
}

/* Custom checkbox and radio animations */
.custom-checkbox .form-check-input,
.custom-radio .form-check-input {
    transition: all 0.2s ease-in-out;
}

.custom-checkbox .form-check-input:checked,
.custom-radio .form-check-input:checked {
    transform: scale(1.1);
}

/* Form validation summary */
#validationSummary .alert {
    border-left: 4px solid var(--bs-danger);
}

#validationSummary ul {
    padding-left: 1.5rem;
}

#validationSummary li {
    margin-bottom: 0.25rem;
}