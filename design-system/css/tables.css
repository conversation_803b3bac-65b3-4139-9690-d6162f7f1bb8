/* Table Component Styles */

/* Enhanced Table Styling */
.table {
    --bs-table-striped-bg: rgba(67, 37, 27, 0.05);
    --bs-table-hover-bg: rgba(67, 37, 27, 0.075);
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--bs-light);
    border-bottom: 2px solid var(--bs-primary);
    font-weight: 600;
    color: var(--bs-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

.table tbody tr:hover {
    background-color: var(--bs-table-hover-bg);
}

/* Sortable Column Styling */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 2rem !important;
}

.sortable:hover {
    background-color: rgba(67, 37, 27, 0.1);
}

.sortable i {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.sortable:hover i {
    opacity: 1;
}

.sortable.asc i,
.sortable.desc i {
    opacity: 1;
    color: var(--bs-primary);
}

/* Loading State */
.table-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

.loading-spinner {
    text-align: center;
}

/* Empty State */
.table-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.empty-state {
    text-align: center;
    max-width: 400px;
}

.empty-state i {
    opacity: 0.6;
}

/* Error State */
.table-error {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.error-state {
    text-align: center;
    max-width: 400px;
}

/* No Results State */
.table-no-results {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;
}

.no-results-state {
    text-align: center;
    max-width: 400px;
}

/* Responsive Table Enhancements */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Stacked Table for Mobile */
@media (max-width: 768px) {
    .table-stacked thead {
        display: none;
    }
    
    .table-stacked tbody,
    .table-stacked tr,
    .table-stacked td {
        display: block;
        width: 100%;
    }
    
    .table-stacked tr {
        border: 1px solid var(--bs-border-color);
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        padding: 1rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .table-stacked td {
        border: none;
        padding: 0.5rem 0;
        position: relative;
        padding-left: 40%;
    }
    
    .table-stacked td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 35%;
        font-weight: 600;
        color: var(--bs-primary);
    }
}

/* Action Buttons */
.btn-group-action {
    display: flex;
    gap: 0.25rem;
}

.btn-group-action .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Bulk Actions */
.bulk-actions {
    background-color: var(--bs-light);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid var(--bs-border-color);
}

.bulk-actions.active {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: var(--bs-primary);
}

/* Table Status Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Filter Controls */
.table-filters {
    background-color: var(--bs-light);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--bs-border-color);
}

/* Search Input Enhancement */
.search-input-wrapper {
    position: relative;
}

.search-input-wrapper .form-control {
    padding-left: 2.5rem;
}

.search-input-wrapper .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--bs-secondary);
    z-index: 5;
}

/* Professional Card Styling */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h5 {
    color: var(--bs-primary);
    font-weight: 600;
}

/* Animation for row actions */
.table tbody tr {
    transition: all 0.2s ease-in-out;
}

.table tbody tr:hover .btn {
    transform: translateY(-1px);
}

/* Professional spacing */
.table th,
.table td {
    padding: 0.875rem;
    vertical-align: middle;
}

/* Checkbox styling */
.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* Selection highlight */
.table tbody tr.selected {
    background-color: rgba(13, 110, 253, 0.1);
}

/* Table border improvements */
.table-bordered {
    border: 1px solid var(--bs-border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--bs-border-color);
}

/* Professional hover effects */
.table-hover tbody tr:hover {
    background-color: var(--bs-table-hover-bg);
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
} 