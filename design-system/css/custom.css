/**
 * CozyWish Bootstrap Customizations
 * 
 * This file customizes Bootstrap components using CozyWish design tokens.
 * Import tokens.css before this file to ensure tokens are available.
 * 
 * Organization:
 * 1. Bootstrap CSS Variable Overrides
 * 2. Component Customizations  
 * 3. Utility Classes
 * 4. Focus States & Accessibility
 */

/* ===== BOOTSTRAP CSS VARIABLE OVERRIDES ===== */

:root {
  /* ===== PRIMARY BRAND COLORS ===== */
  --bs-primary: var(--cw-color-primary);
  --bs-primary-rgb: var(--cw-color-primary-rgb);
  --bs-secondary: var(--cw-color-secondary);
  --bs-secondary-rgb: var(--cw-color-secondary-rgb);
  
  /* ===== NEUTRAL COLORS ===== */
  --bs-light: var(--cw-color-accent);
  --bs-light-rgb: var(--cw-color-accent-rgb);
  --bs-dark: var(--cw-color-black);
  --bs-dark-rgb: var(--cw-color-black-rgb);
  
  /* ===== SEMANTIC COLORS ===== */
  --bs-success: var(--cw-color-success);
  --bs-success-rgb: var(--cw-color-success-rgb);
  --bs-warning: var(--cw-color-warning);
  --bs-warning-rgb: var(--cw-color-warning-rgb);
  --bs-danger: var(--cw-color-danger);
  --bs-danger-rgb: var(--cw-color-danger-rgb);
  --bs-info: var(--cw-color-info);
  --bs-info-rgb: var(--cw-color-info-rgb);
  
  /* ===== BACKGROUND COLORS ===== */
  --bs-body-bg: var(--cw-color-background);
  --bs-body-color: var(--cw-color-text-primary);
  --bs-body-color-rgb: var(--cw-color-text-primary-rgb);
  --bs-heading-color: var(--cw-color-text-heading);
  
  /* ===== TYPOGRAPHY ===== */
  --bs-font-family-sans-serif: var(--cw-font-family-primary);
  --bs-font-family-monospace: var(--cw-font-family-monospace);
  --bs-font-weight-lighter: var(--cw-font-weight-light);
  --bs-font-weight-light: var(--cw-font-weight-light);
  --bs-font-weight-normal: var(--cw-font-weight-medium);
  --bs-font-weight-bold: var(--cw-font-weight-bold);
  --bs-font-weight-bolder: var(--cw-font-weight-extrabold);
  --bs-line-height-base: var(--cw-line-height-relaxed);
  --bs-line-height-sm: var(--cw-line-height-snug);
  --bs-line-height-lg: var(--cw-line-height-loose);
  
  /* ===== BORDERS ===== */
  --bs-border-color: var(--cw-color-border);
  --bs-border-color-translucent: var(--cw-color-border-translucent);
  --bs-border-radius: var(--cw-radius-md);
  --bs-border-radius-sm: var(--cw-radius-sm);
  --bs-border-radius-lg: var(--cw-radius-lg);
  --bs-border-radius-xl: var(--cw-radius-xl);
  --bs-border-radius-2xl: var(--cw-radius-2xl);
  --bs-border-radius-pill: var(--cw-radius-full);
  
  /* ===== COMPONENT COLORS ===== */
  
  /* Buttons */
  --bs-btn-color: var(--cw-color-text-inverse);
  --bs-btn-bg: var(--cw-color-primary);
  --bs-btn-border-color: var(--cw-color-primary);
  --bs-btn-hover-color: var(--cw-color-text-inverse);
  --bs-btn-hover-bg: var(--cw-color-primary-light);
  --bs-btn-hover-border-color: var(--cw-color-primary-light);
  --bs-btn-focus-shadow-rgb: var(--cw-color-primary-rgb);
  --bs-btn-active-color: var(--cw-color-text-inverse);
  --bs-btn-active-bg: var(--cw-color-primary-light);
  --bs-btn-active-border-color: var(--cw-color-primary-light);
  
  /* Forms */
  --bs-form-control-bg: var(--cw-color-white);
  --bs-form-control-color: var(--cw-color-text-primary);
  --bs-form-control-border-color: var(--cw-color-border);
  --bs-form-control-focus-border-color: var(--cw-color-primary);
  --bs-form-control-focus-box-shadow: var(--cw-shadow-focus-primary);
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2343251B' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  
  /* Cards */
  --bs-card-bg: var(--cw-color-white);
  --bs-card-border-color: var(--cw-color-border);
  --bs-card-cap-bg: var(--cw-color-accent);
  
  /* Navbars */
  --bs-navbar-brand-color: var(--cw-color-primary);
  --bs-navbar-brand-hover-color: var(--cw-color-primary-light);
  --bs-navbar-nav-link-color: var(--cw-color-text-primary);
  --bs-navbar-nav-link-hover-color: var(--cw-color-primary);
  --bs-navbar-nav-link-active-color: var(--cw-color-primary);
  
  /* Dropdowns */
  --bs-dropdown-bg: var(--cw-color-white);
  --bs-dropdown-border-color: var(--cw-color-border);
  --bs-dropdown-link-color: var(--cw-color-text-primary);
  --bs-dropdown-link-hover-color: var(--cw-color-primary);
  --bs-dropdown-link-hover-bg: var(--cw-color-accent);
  --bs-dropdown-link-active-color: var(--cw-color-text-inverse);
  --bs-dropdown-link-active-bg: var(--cw-color-primary);
  
  /* Modals */
  --bs-modal-bg: var(--cw-color-white);
  --bs-modal-header-border-color: var(--cw-color-border);
  --bs-modal-footer-border-color: var(--cw-color-border);
  
  /* Tables */
  --bs-table-bg: var(--cw-color-white);
  --bs-table-border-color: var(--cw-color-border);
  --bs-table-striped-bg: var(--cw-color-accent);
  --bs-table-hover-bg: var(--cw-color-gray-50);
  
  /* Progress */
  --bs-progress-bg: var(--cw-color-border);
  --bs-progress-bar-bg: var(--cw-color-primary);
  
  /* List Groups */
  --bs-list-group-bg: var(--cw-color-white);
  --bs-list-group-border-color: var(--cw-color-border);
  --bs-list-group-action-hover-bg: var(--cw-color-accent);
  --bs-list-group-action-active-bg: var(--cw-color-primary);
  --bs-list-group-action-active-color: var(--cw-color-text-inverse);
  
  /* Badges */
  --bs-badge-color: var(--cw-color-text-inverse);
  --bs-badge-bg: var(--cw-color-secondary);
  
  /* Links */
  --bs-link-color: var(--cw-color-primary);
  --bs-link-hover-color: var(--cw-color-primary-light);
  
  /* Alert light variants */
  --bs-success-light: var(--cw-color-success-light);
  --bs-warning-light: var(--cw-color-warning-light);
  --bs-danger-light: var(--cw-color-danger-light);
  --bs-info-light: var(--cw-color-info-light);
}

/* ===== COMPONENT CUSTOMIZATIONS ===== */

/* ===== BUTTONS ===== */
.btn {
  font-weight: var(--cw-button-font-weight);
  border-width: var(--cw-button-border-width);
  border-radius: var(--cw-button-border-radius);
  padding: var(--cw-button-padding-y) var(--cw-button-padding-x);
  font-size: var(--cw-button-font-size);
  line-height: var(--cw-button-line-height);
  box-shadow: var(--cw-shadow-sm);
  transition: var(--cw-transition-all);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--cw-shadow-lg);
}

.btn:active,
.btn.active {
  transform: translateY(0);
  box-shadow: var(--cw-shadow-sm);
}

.btn:focus,
.btn:focus-visible {
  box-shadow: var(--cw-shadow-focus-primary);
  outline: none;
}

.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: var(--cw-shadow-xs);
}

/* Button variants */
.btn-outline-primary {
  color: var(--cw-color-primary);
  border-color: var(--cw-color-primary);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--cw-color-primary);
  border-color: var(--cw-color-primary);
  color: var(--cw-color-text-inverse);
}

/* Light button variant */
.btn-light {
  background-color: var(--cw-color-accent);
  border-color: var(--cw-color-border);
  color: var(--cw-color-primary);
}

.btn-light:hover {
  background-color: var(--cw-color-primary);
  border-color: var(--cw-color-primary);
  color: var(--cw-color-text-inverse);
}

/* Button sizes */
.btn-lg {
  padding: var(--cw-spacing-3) var(--cw-spacing-6);
  font-size: var(--cw-font-size-lg);
  border-radius: var(--cw-radius-lg);
}

.btn-sm {
  padding: var(--cw-spacing-1-5) var(--cw-spacing-3);
  font-size: var(--cw-font-size-sm);
  border-radius: var(--cw-radius-base);
  border-width: 1px;
}

/* ===== FORMS ===== */
.form-control,
.form-select {
  padding: var(--cw-form-control-padding-y) var(--cw-form-control-padding-x);
  font-size: var(--cw-form-control-font-size);
  line-height: var(--cw-form-control-line-height);
  border-radius: var(--cw-form-control-border-radius);
  border-width: var(--cw-form-control-border-width);
  transition: var(--cw-transition-colors);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--cw-color-primary);
  box-shadow: var(--cw-shadow-focus-primary);
}

.form-label {
  font-weight: var(--cw-font-weight-semibold);
  color: var(--cw-color-text-heading);
  margin-bottom: var(--cw-spacing-2);
}

.form-text {
  color: var(--cw-color-text-secondary);
  font-size: var(--cw-font-size-sm);
}

/* Custom checkbox and radio */
.form-check-input:checked {
  background-color: var(--cw-color-primary);
  border-color: var(--cw-color-primary);
}

.form-check-input:focus {
  box-shadow: var(--cw-shadow-focus-primary);
}

/* ===== CARDS ===== */
.card {
  border-radius: var(--cw-card-border-radius);
  border-width: var(--cw-card-border-width);
  box-shadow: var(--cw-card-shadow);
  overflow: hidden;
}

.card-header {
  background-color: var(--cw-color-accent);
  border-bottom-color: var(--cw-color-border);
  padding: var(--cw-card-padding);
}

.card-body {
  padding: var(--cw-card-padding);
}

.card-title {
  color: var(--cw-color-text-heading);
  font-weight: var(--cw-font-weight-bold);
  margin-bottom: var(--cw-spacing-3);
}

.card-subtitle {
  color: var(--cw-color-text-secondary);
  font-weight: var(--cw-font-weight-medium);
}

/* ===== NAVIGATION ===== */
.navbar-brand {
  font-weight: var(--cw-font-weight-bold);
  color: var(--cw-color-primary);
}

.navbar-brand:hover,
.navbar-brand:focus {
  color: var(--cw-color-primary-light);
}

.navbar-nav .nav-link {
  font-weight: var(--cw-nav-link-font-weight);
  padding: var(--cw-nav-link-padding-y) var(--cw-nav-link-padding-x);
  border-radius: var(--cw-nav-link-border-radius);
  transition: var(--cw-transition-colors);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
  color: var(--cw-color-primary);
  background-color: var(--cw-color-accent);
}

.navbar-nav .nav-link.active {
  color: var(--cw-color-primary);
  font-weight: var(--cw-font-weight-semibold);
}

/* ===== BADGES ===== */
.badge {
  padding: var(--cw-badge-padding-y) var(--cw-badge-padding-x);
  font-size: var(--cw-badge-font-size);
  font-weight: var(--cw-badge-font-weight);
  border-radius: var(--cw-badge-border-radius);
}

/* ===== ALERTS ===== */
.alert {
  border-radius: var(--cw-radius-lg);
  border-width: 1px;
  padding: var(--cw-spacing-4);
}

.alert-primary {
  color: var(--cw-color-primary);
  background-color: rgba(var(--cw-color-primary-rgb), 0.1);
  border-color: rgba(var(--cw-color-primary-rgb), 0.2);
}

.alert-success {
  color: var(--cw-color-success);
  background-color: var(--cw-color-success-light);
  border-color: var(--cw-color-success);
}

.alert-warning {
  color: var(--cw-color-warning);
  background-color: var(--cw-color-warning-light);
  border-color: var(--cw-color-warning);
}

.alert-danger {
  color: var(--cw-color-danger);
  background-color: var(--cw-color-danger-light);
  border-color: var(--cw-color-danger);
}

.alert-info {
  color: var(--cw-color-info);
  background-color: var(--cw-color-info-light);
  border-color: var(--cw-color-info);
}

/* ===== TABLES ===== */
.table {
  border-radius: var(--cw-radius-lg);
  overflow: hidden;
}

.table th {
  background-color: var(--cw-color-accent);
  color: var(--cw-color-text-heading);
  font-weight: var(--cw-font-weight-semibold);
  border-color: var(--cw-color-border);
}

.table td {
  border-color: var(--cw-color-border);
}

/* ===== DROPDOWNS ===== */
.dropdown-menu {
  border-radius: var(--cw-radius-lg);
  box-shadow: var(--cw-shadow-xl);
  border-width: 1px;
  padding: var(--cw-spacing-2);
}

.dropdown-item {
  border-radius: var(--cw-radius-md);
  padding: var(--cw-spacing-2) var(--cw-spacing-3);
  transition: var(--cw-transition-colors);
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--cw-color-accent);
  color: var(--cw-color-primary);
}

.dropdown-item.active {
  background-color: var(--cw-color-primary);
  color: var(--cw-color-text-inverse);
}

/* ===== MODALS ===== */
.modal-content {
  border-radius: var(--cw-radius-xl);
  border: none;
  box-shadow: var(--cw-shadow-2xl);
}

.modal-header {
  border-bottom-color: var(--cw-color-border);
  padding: var(--cw-spacing-6);
}

.modal-body {
  padding: var(--cw-spacing-6);
}

.modal-footer {
  border-top-color: var(--cw-color-border);
  padding: var(--cw-spacing-6);
}

.modal-title {
  color: var(--cw-color-text-heading);
  font-weight: var(--cw-font-weight-bold);
}

/* ===== UTILITY CLASSES ===== */

/* Color utilities */
.text-brand {
  color: var(--cw-color-primary) !important;
}

.text-brand-light {
  color: var(--cw-color-primary-light) !important;
}

.bg-brand {
  background-color: var(--cw-color-primary) !important;
  color: var(--cw-color-text-inverse) !important;
}

.bg-accent {
  background-color: var(--cw-color-accent) !important;
}

.border-brand {
  border-color: var(--cw-color-primary) !important;
}

/* Shadow utilities */
.shadow-brand {
  box-shadow: var(--cw-shadow-base) !important;
}

.shadow-brand-lg {
  box-shadow: var(--cw-shadow-lg) !important;
}

.shadow-brand-xl {
  box-shadow: var(--cw-shadow-xl) !important;
}

/* Typography utilities */
.fw-medium {
  font-weight: var(--cw-font-weight-medium) !important;
}

.fw-semibold {
  font-weight: var(--cw-font-weight-semibold) !important;
}

/* ===== FOCUS STATES & ACCESSIBILITY ===== */

/* Enhanced focus styles for better accessibility */
.btn:focus-visible,
.form-control:focus-visible,
.form-select:focus-visible,
.nav-link:focus-visible,
.dropdown-item:focus-visible {
  outline: 2px solid var(--cw-color-primary);
  outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .form-control,
  .form-select,
  .nav-link,
  .dropdown-item {
    transition: none;
  }
  
  .btn:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }
  
  .form-control,
  .form-select {
    border-width: 2px;
  }
}

/* Dark mode support (future-proofing) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables can be added here when needed */
}

/* ===== SELECTION STYLES ===== */
::selection {
  background-color: var(--cw-color-primary);
  color: var(--cw-color-text-inverse);
}

::-moz-selection {
  background-color: var(--cw-color-primary);
  color: var(--cw-color-text-inverse);
}

/* ===== PLACEHOLDER STYLES ===== */
::placeholder {
  color: var(--cw-color-text-muted);
  opacity: 1;
}

::-webkit-input-placeholder {
  color: var(--cw-color-text-muted);
  opacity: 1;
}

::-moz-placeholder {
  color: var(--cw-color-text-muted);
  opacity: 1;
}

:-ms-input-placeholder {
  color: var(--cw-color-text-muted);
  opacity: 1;
}
  





