/**
 * CozyWish Design Tokens
 * 
 * This file contains all design tokens for the CozyWish design system.
 * These tokens should be used throughout the system for consistency.
 * 
 * Naming Convention: --cw-[category]-[name]-[variant]
 * Categories: color, font, spacing, size, radius, shadow, transition
 */

:root {
  /* ===== COLOR TOKENS ===== */
  
  /* Brand Colors */
  --cw-color-primary: #43251B;
  --cw-color-primary-rgb: 67, 37, 27;
  --cw-color-primary-light: #5A342A;
  --cw-color-primary-dark: #2F1A12;
  
  --cw-color-secondary: #5A342A;
  --cw-color-secondary-rgb: 90, 52, 42;
  --cw-color-secondary-light: #6B4033;
  --cw-color-secondary-dark: #4A2B22;
  
  --cw-color-accent: #FEF6F0;
  --cw-color-accent-rgb: 254, 246, 240;
  --cw-color-accent-light: #FFFFFF;
  --cw-color-accent-dark: #F5E8DA;
  
  /* Neutral Colors */
  --cw-color-white: #FFFFFF;
  --cw-color-white-rgb: 255, 255, 255;
  
  --cw-color-black: #222222;
  --cw-color-black-rgb: 34, 34, 34;
  
  --cw-color-gray-50: #F8F9FA;
  --cw-color-gray-100: #F1F3F4;
  --cw-color-gray-200: #E9ECEF;
  --cw-color-gray-300: #DEE2E6;
  --cw-color-gray-400: #CED4DA;
  --cw-color-gray-500: #ADB5BD;
  --cw-color-gray-600: #6C757D;
  --cw-color-gray-700: #495057;
  --cw-color-gray-800: #343A40;
  --cw-color-gray-900: #212529;
  
  /* Border Colors */
  --cw-color-border: #E0DFDE;
  --cw-color-border-rgb: 224, 223, 222;
  --cw-color-border-light: #F1F0EF;
  --cw-color-border-dark: #D0CFCE;
  --cw-color-border-translucent: rgba(224, 223, 222, 0.175);
  
  /* Background Colors */
  --cw-color-background: #F8F9FA;
  --cw-color-background-rgb: 248, 249, 250;
  --cw-color-background-dark: #FFFFFF;
  --cw-color-background-alt: #FEF6F0;
  
  /* Text Colors */
  --cw-color-text-primary: #222222;
  --cw-color-text-primary-rgb: 34, 34, 34;
  --cw-color-text-secondary: #6C757D;
  --cw-color-text-muted: #ADB5BD;
  --cw-color-text-inverse: #FFFFFF;
  --cw-color-text-heading: #43251B;
  
  /* Semantic Colors */
  --cw-color-success: #2D5A3D;
  --cw-color-success-rgb: 45, 90, 61;
  --cw-color-success-light: #E8F5E8;
  --cw-color-success-light-rgb: 232, 245, 232;
  --cw-color-success-dark: #1E3D29;
  
  --cw-color-warning: #B8860B;
  --cw-color-warning-rgb: 184, 134, 11;
  --cw-color-warning-light: #FFF8E1;
  --cw-color-warning-light-rgb: 255, 248, 225;
  --cw-color-warning-dark: #9A7209;
  
  --cw-color-danger: #8B2635;
  --cw-color-danger-rgb: 139, 38, 53;
  --cw-color-danger-light: #FDE8E8;
  --cw-color-danger-light-rgb: 253, 232, 232;
  --cw-color-danger-dark: #6B1D28;
  
  --cw-color-info: #2C5F8A;
  --cw-color-info-rgb: 44, 95, 138;
  --cw-color-info-light: #E3F2FD;
  --cw-color-info-light-rgb: 227, 242, 253;
  --cw-color-info-dark: #1E4261;
  
  /* ===== TYPOGRAPHY TOKENS ===== */
  
  /* Font Families */
  --cw-font-family-primary: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  --cw-font-family-secondary: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
  --cw-font-family-monospace: "SFMono-Regular", "Menlo", "Monaco", "Consolas", "Liberation Mono", "Courier New", monospace;
  
  /* Font Sizes */
  --cw-font-size-xs: 0.75rem;      /* 12px */
  --cw-font-size-sm: 0.875rem;     /* 14px */
  --cw-font-size-base: 1rem;       /* 16px */
  --cw-font-size-md: 1.125rem;     /* 18px */
  --cw-font-size-lg: 1.25rem;      /* 20px */
  --cw-font-size-xl: 1.5rem;       /* 24px */
  --cw-font-size-2xl: 1.875rem;    /* 30px */
  --cw-font-size-3xl: 2.25rem;     /* 36px */
  --cw-font-size-4xl: 3rem;        /* 48px */
  --cw-font-size-5xl: 3.75rem;     /* 60px */
  
  /* Font Weights */
  --cw-font-weight-thin: 100;
  --cw-font-weight-light: 300;
  --cw-font-weight-normal: 400;
  --cw-font-weight-medium: 500;
  --cw-font-weight-semibold: 600;
  --cw-font-weight-bold: 700;
  --cw-font-weight-extrabold: 800;
  --cw-font-weight-black: 900;
  
  /* Line Heights */
  --cw-line-height-tight: 1.25;
  --cw-line-height-snug: 1.375;
  --cw-line-height-normal: 1.5;
  --cw-line-height-relaxed: 1.625;
  --cw-line-height-loose: 2;
  
  /* Letter Spacing */
  --cw-letter-spacing-tighter: -0.05em;
  --cw-letter-spacing-tight: -0.025em;
  --cw-letter-spacing-normal: 0em;
  --cw-letter-spacing-wide: 0.025em;
  --cw-letter-spacing-wider: 0.05em;
  --cw-letter-spacing-widest: 0.1em;
  
  /* ===== SPACING TOKENS ===== */
  
  --cw-spacing-0: 0rem;            /* 0px */
  --cw-spacing-px: 0.0625rem;      /* 1px */
  --cw-spacing-0-5: 0.125rem;      /* 2px */
  --cw-spacing-1: 0.25rem;         /* 4px */
  --cw-spacing-1-5: 0.375rem;      /* 6px */
  --cw-spacing-2: 0.5rem;          /* 8px */
  --cw-spacing-2-5: 0.625rem;      /* 10px */
  --cw-spacing-3: 0.75rem;         /* 12px */
  --cw-spacing-3-5: 0.875rem;      /* 14px */
  --cw-spacing-4: 1rem;            /* 16px */
  --cw-spacing-5: 1.25rem;         /* 20px */
  --cw-spacing-6: 1.5rem;          /* 24px */
  --cw-spacing-7: 1.75rem;         /* 28px */
  --cw-spacing-8: 2rem;            /* 32px */
  --cw-spacing-9: 2.25rem;         /* 36px */
  --cw-spacing-10: 2.5rem;         /* 40px */
  --cw-spacing-12: 3rem;           /* 48px */
  --cw-spacing-14: 3.5rem;         /* 56px */
  --cw-spacing-16: 4rem;           /* 64px */
  --cw-spacing-20: 5rem;           /* 80px */
  --cw-spacing-24: 6rem;           /* 96px */
  --cw-spacing-28: 7rem;           /* 112px */
  --cw-spacing-32: 8rem;           /* 128px */
  
  /* ===== SIZE TOKENS ===== */
  
  /* Width */
  --cw-width-auto: auto;
  --cw-width-full: 100%;
  --cw-width-screen: 100vw;
  --cw-width-min: min-content;
  --cw-width-max: max-content;
  --cw-width-fit: fit-content;
  
  /* Height */
  --cw-height-auto: auto;
  --cw-height-full: 100%;
  --cw-height-screen: 100vh;
  --cw-height-min: min-content;
  --cw-height-max: max-content;
  --cw-height-fit: fit-content;
  
  /* ===== BORDER RADIUS TOKENS ===== */
  
  --cw-radius-none: 0;
  --cw-radius-sm: 0.125rem;        /* 2px */
  --cw-radius-base: 0.25rem;       /* 4px */
  --cw-radius-md: 0.375rem;        /* 6px */
  --cw-radius-lg: 0.5rem;          /* 8px */
  --cw-radius-xl: 0.75rem;         /* 12px */
  --cw-radius-2xl: 1rem;           /* 16px */
  --cw-radius-3xl: 1.5rem;         /* 24px */
  --cw-radius-full: 9999px;
  
  /* ===== SHADOW TOKENS ===== */
  
  --cw-shadow-xs: 0 1px 2px 0 rgba(67, 37, 27, 0.05);
  --cw-shadow-sm: 0 1px 3px 0 rgba(67, 37, 27, 0.1), 0 1px 2px 0 rgba(67, 37, 27, 0.06);
  --cw-shadow-base: 0 4px 6px -1px rgba(67, 37, 27, 0.1), 0 2px 4px -1px rgba(67, 37, 27, 0.06);
  --cw-shadow-md: 0 4px 6px -1px rgba(67, 37, 27, 0.1), 0 2px 4px -1px rgba(67, 37, 27, 0.06);
  --cw-shadow-lg: 0 10px 15px -3px rgba(67, 37, 27, 0.1), 0 4px 6px -2px rgba(67, 37, 27, 0.05);
  --cw-shadow-xl: 0 20px 25px -5px rgba(67, 37, 27, 0.1), 0 10px 10px -5px rgba(67, 37, 27, 0.04);
  --cw-shadow-2xl: 0 25px 50px -12px rgba(67, 37, 27, 0.25);
  --cw-shadow-inner: inset 0 2px 4px 0 rgba(67, 37, 27, 0.06);
  --cw-shadow-none: 0 0 #0000;
  
  /* Focus Shadows */
  --cw-shadow-focus: 0 0 0 3px rgba(67, 37, 27, 0.1);
  --cw-shadow-focus-primary: 0 0 0 3px rgba(67, 37, 27, 0.25);
  --cw-shadow-focus-danger: 0 0 0 3px rgba(139, 38, 53, 0.25);
  --cw-shadow-focus-success: 0 0 0 3px rgba(45, 90, 61, 0.25);
  --cw-shadow-focus-warning: 0 0 0 3px rgba(184, 134, 11, 0.25);
  --cw-shadow-focus-info: 0 0 0 3px rgba(44, 95, 138, 0.25);
  
  /* ===== TRANSITION TOKENS ===== */
  
  --cw-transition-duration-fast: 150ms;
  --cw-transition-duration-base: 200ms;
  --cw-transition-duration-slow: 300ms;
  --cw-transition-duration-slower: 500ms;
  
  --cw-transition-timing-ease: ease;
  --cw-transition-timing-ease-in: ease-in;
  --cw-transition-timing-ease-out: ease-out;
  --cw-transition-timing-ease-in-out: ease-in-out;
  --cw-transition-timing-linear: linear;
  
  /* Common Transitions */
  --cw-transition-all: all var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out);
  --cw-transition-colors: background-color var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out), 
                          border-color var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out), 
                          color var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out);
  --cw-transition-opacity: opacity var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out);
  --cw-transition-transform: transform var(--cw-transition-duration-base) var(--cw-transition-timing-ease-in-out);
  
  /* ===== Z-INDEX TOKENS ===== */
  
  --cw-z-index-dropdown: 1000;
  --cw-z-index-sticky: 1020;
  --cw-z-index-fixed: 1030;
  --cw-z-index-modal-backdrop: 1040;
  --cw-z-index-offcanvas: 1050;
  --cw-z-index-modal: 1060;
  --cw-z-index-popover: 1070;
  --cw-z-index-tooltip: 1080;
  
  /* ===== BREAKPOINT TOKENS ===== */
  
  --cw-breakpoint-xs: 0;
  --cw-breakpoint-sm: 576px;
  --cw-breakpoint-md: 768px;
  --cw-breakpoint-lg: 992px;
  --cw-breakpoint-xl: 1200px;
  --cw-breakpoint-xxl: 1400px;
}

/* ===== COMPONENT-SPECIFIC TOKENS ===== */

:root {
  /* Button Tokens */
  --cw-button-padding-x: var(--cw-spacing-4);
  --cw-button-padding-y: var(--cw-spacing-2);
  --cw-button-font-weight: var(--cw-font-weight-medium);
  --cw-button-border-radius: var(--cw-radius-md);
  --cw-button-border-width: 2px;
  --cw-button-font-size: var(--cw-font-size-base);
  --cw-button-line-height: var(--cw-line-height-normal);
  
  /* Form Tokens */
  --cw-form-control-padding-x: var(--cw-spacing-3);
  --cw-form-control-padding-y: var(--cw-spacing-2);
  --cw-form-control-border-radius: var(--cw-radius-md);
  --cw-form-control-border-width: 1px;
  --cw-form-control-font-size: var(--cw-font-size-base);
  --cw-form-control-line-height: var(--cw-line-height-normal);
  
  /* Card Tokens */
  --cw-card-padding: var(--cw-spacing-6);
  --cw-card-border-radius: var(--cw-radius-lg);
  --cw-card-border-width: 1px;
  --cw-card-shadow: var(--cw-shadow-base);
  
  /* Badge Tokens */
  --cw-badge-padding-x: var(--cw-spacing-2);
  --cw-badge-padding-y: var(--cw-spacing-1);
  --cw-badge-border-radius: var(--cw-radius-full);
  --cw-badge-font-size: var(--cw-font-size-xs);
  --cw-badge-font-weight: var(--cw-font-weight-medium);
  
  /* Navigation Tokens */
  --cw-nav-link-padding-x: var(--cw-spacing-4);
  --cw-nav-link-padding-y: var(--cw-spacing-2);
  --cw-nav-link-font-weight: var(--cw-font-weight-medium);
  --cw-nav-link-border-radius: var(--cw-radius-md);
} 