/* ================================================
   PRODUCT FEEDBACK COMPONENTS CSS
   CozyWish Store - Professional Product Rating & Review System
   ================================================ */

/* ==== PRODUCT RATING DISPLAY ==== */

/* Rating Summary Styles */
.rating-summary {
    padding: 2rem;
    background: #ffffff;
    border-radius: 1rem;
    border: 2px solid var(--bs-border-color);
}

.average-rating {
    font-size: 3rem;
    font-weight: 700;
    color: var(--bs-primary);
    margin-bottom: 0.5rem;
}

.star-display i {
    font-size: 1.5rem;
    margin: 0 2px;
}

.review-count {
    color: var(--bs-secondary);
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Rating Breakdown Styles */
.rating-breakdown {
    padding: 1rem;
}

.rating-bar {
    margin-bottom: 0.75rem;
}

.rating-label {
    font-weight: 600;
    color: var(--bs-primary);
    width: 20px;
    text-align: center;
}

.rating-percentage {
    font-weight: 500;
    color: var(--bs-secondary);
    width: 40px;
    text-align: right;
}

.progress {
    height: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.progress-bar {
    background: linear-gradient(90deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border-radius: 4px;
}

/* ==== INTERACTIVE RATING COMPONENTS ==== */

/* Product Rating Input */
.product-rating-input {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    margin: 0.5rem 0;
}

.rating-star {
    font-size: 1.5rem;
    color: #E0DFDE;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rating-star:hover {
    color: #ffc107 !important;
    transform: scale(1.1);
}

.rating-star.text-warning {
    color: #ffc107 !important;
}

.rating-small .rating-star {
    font-size: 1.2rem;
}

.rating-text {
    color: var(--bs-secondary);
    font-weight: 500;
    margin-left: 0.5rem;
    font-size: 0.9rem;
}

/* Product Rating Display (Read-only) */
.product-rating-display {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;
}

.rating-stars i {
    font-size: 1.3rem;
    margin: 0 2px;
}

.rating-value {
    color: var(--bs-secondary);
    font-weight: 500;
    font-size: 0.95rem;
}

/* ==== REVIEW COMPONENTS ==== */

/* Review Filters */
.card-header {
    background: #ffffff;
    border-bottom: 2px solid var(--bs-border-color);
}

.card-title {
    color: var(--bs-primary);
    font-weight: 600;
    margin-bottom: 0;
}

/* Review Items */
.review-item {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--bs-border-color);
}

.review-item:last-child {
    border-bottom: none;
}

.reviewer-avatar i {
    font-size: 2.5rem;
    color: var(--bs-primary);
}

.reviewer-name {
    color: var(--bs-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.review-meta {
    font-size: 0.9rem;
}

.review-rating i {
    font-size: 1rem;
    margin: 0 1px;
}

.review-date {
    font-size: 0.85rem;
}

.review-title {
    color: var(--bs-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.review-text {
    color: var(--bs-body-color);
    line-height: 1.6;
    margin-bottom: 0;
}

.review-helpful {
    display: flex;
    align-items: center;
}

.review-helpful .btn-link {
    color: var(--bs-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.review-helpful .btn-link:hover {
    color: var(--bs-primary);
    background-color: transparent;
}

.review-helpful .btn-link:focus {
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

.verified-purchase {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
    background-color: var(--bs-success) !important;
}

/* Review Actions */
.review-actions .btn-link {
    color: var(--bs-secondary);
    padding: 0.25rem;
    border-radius: 0.375rem;
}

.review-actions .btn-link:hover {
    color: var(--bs-primary);
    background-color: transparent;
}

/* ==== REVIEW FORM ==== */

.review-form {
    padding: 1rem 0;
}

.form-label {
    color: var(--bs-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid var(--bs-border-color);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

.form-select {
    border: 2px solid var(--bs-border-color);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

.form-check-input:checked {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.form-check-input:focus {
    border-color: var(--bs-secondary);
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

.form-check-label {
    color: var(--bs-body-color);
    font-weight: 500;
}

/* ==== CARD STYLING ==== */

.card {
    border: 2px solid var(--bs-border-color);
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(67, 37, 27, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.card:hover {
    box-shadow: 0 8px 15px rgba(67, 37, 27, 0.15);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.card-body {
    padding: 2rem;
}

/* ==== BUTTON STYLING ==== */

.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border: 2px solid var(--bs-primary);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--bs-secondary) 0%, var(--bs-primary) 100%);
    border-color: var(--bs-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(67, 37, 27, 0.2);
}

.btn-outline-primary {
    border: 2px solid var(--bs-primary);
    color: var(--bs-primary);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
}

.btn-outline-primary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--bs-secondary);
    border: 2px solid var(--bs-secondary);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
}

.btn-secondary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    transform: translateY(-1px);
}

/* ==== RESPONSIVE DESIGN ==== */

@media (max-width: 768px) {
    .rating-summary {
        padding: 1.5rem;
    }
    
    .average-rating {
        font-size: 2.5rem;
    }
    
    .star-display i {
        font-size: 1.2rem;
    }
    
    .rating-star {
        font-size: 1.3rem;
    }
    
    .reviewer-avatar i {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .review-item {
        padding: 1rem 0;
    }
}

@media (max-width: 576px) {
    .rating-breakdown .rating-bar {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }
    
    .progress {
        width: 100%;
    }
    
    .reviewer-info {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
    }
    
    .review-footer {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* ==== ACCESSIBILITY IMPROVEMENTS ==== */

.rating-star:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

.btn:focus {
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.5);
}

/* ==== ANIMATION EFFECTS ==== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.review-item {
    animation: fadeInUp 0.4s ease-out;
}

/* ==== UTILITY CLASSES ==== */

.text-primary {
    color: var(--bs-primary) !important;
}

.text-secondary {
    color: var(--bs-secondary) !important;
}

.bg-light {
    background-color: #ffffff !important;
}

/* ==== PROFESSIONAL ENHANCEMENTS ==== */

.card-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-group .btn {
    border-left: none;
}

.input-group .form-control:focus + .btn {
    border-color: var(--bs-primary);
}

.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
}

/* Star rating hover states */
.product-rating-input:hover .rating-star:not(:hover) ~ .rating-star {
    color: #E0DFDE !important;
    transform: scale(1);
}

/* Load more button styling */
.btn-outline-primary i {
    transition: transform 0.2s ease;
}

.btn-outline-primary:hover i {
    transform: translateY(2px);
}

/* Professional spacing */
.mb-4 {
    margin-bottom: 2rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

/* Enhanced focus states for better accessibility */
*:focus {
    outline: none;
}

.form-control:focus,
.form-select:focus,
.btn:focus,
.rating-star:focus {
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

/* Professional typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--bs-primary);
    font-weight: 600;
}

.lead {
    font-weight: 500;
    color: var(--bs-secondary);
}

/* Enhanced card interactions */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(67, 37, 27, 0.15);
} 