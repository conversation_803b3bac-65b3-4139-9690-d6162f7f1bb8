/* ================================
   CozyWish Card Components CSS
   Professional card styling with brand colors
   ================================ */

/* ================================
   CSS Custom Properties (Brand Colors)
   ================================ */
:root {
    /* Primary Brand Colors */
    --cw-primary: #2563eb;
    --cw-primary-dark: #1d4ed8;
    --cw-primary-light: #3b82f6;
    
    /* Secondary Colors */
    --cw-secondary: #64748b;
    --cw-secondary-dark: #475569;
    --cw-secondary-light: #94a3b8;
    
    /* Status Colors */
    --cw-success: #10b981;
    --cw-success-dark: #059669;
    --cw-success-light: #34d399;
    
    --cw-warning: #f59e0b;
    --cw-warning-dark: #d97706;
    --cw-warning-light: #fbbf24;
    
    --cw-danger: #ef4444;
    --cw-danger-dark: #dc2626;
    --cw-danger-light: #f87171;
    
    --cw-info: #06b6d4;
    --cw-info-dark: #0891b2;
    --cw-info-light: #22d3ee;
    
    /* Neutral Colors */
    --cw-light: #f8fafc;
    --cw-dark: #1e293b;
    --cw-gray-100: #f1f5f9;
    --cw-gray-200: #e2e8f0;
    --cw-gray-300: #cbd5e1;
    --cw-gray-400: #94a3b8;
    --cw-gray-500: #64748b;
    --cw-gray-600: #475569;
    --cw-gray-700: #334155;
    --cw-gray-800: #1e293b;
    --cw-gray-900: #0f172a;
    
    /* Shadows */
    --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --cw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --cw-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --cw-border-radius: 0.75rem;
    --cw-border-radius-sm: 0.5rem;
    --cw-border-radius-lg: 1rem;
    
    /* Transitions */
    --cw-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ================================
   Base Card Styles
   ================================ */
.cw-card {
    border: 1px solid var(--cw-gray-200);
    border-radius: var(--cw-border-radius);
    box-shadow: var(--cw-shadow);
    transition: var(--cw-transition);
    background-color: #ffffff;
}

.cw-card:hover {
    box-shadow: var(--cw-shadow-md);
    transform: translateY(-2px);
}

.cw-card-header {
    background-color: var(--cw-gray-50);
    border-bottom: 1px solid var(--cw-gray-200);
    border-radius: var(--cw-border-radius-sm) var(--cw-border-radius-sm) 0 0;
    padding: 1rem 1.25rem;
}

.cw-card-header h6 {
    color: var(--cw-gray-700);
    font-weight: 600;
}

/* ================================
   Custom Buttons
   ================================ */
.cw-btn-primary {
    background-color: var(--cw-primary);
    border-color: var(--cw-primary);
    font-weight: 500;
    transition: var(--cw-transition);
}

.cw-btn-primary:hover {
    background-color: var(--cw-primary-dark);
    border-color: var(--cw-primary-dark);
    transform: translateY(-1px);
}

.cw-btn-success {
    background-color: var(--cw-success);
    border-color: var(--cw-success);
    font-weight: 500;
    transition: var(--cw-transition);
}

.cw-btn-success:hover {
    background-color: var(--cw-success-dark);
    border-color: var(--cw-success-dark);
    transform: translateY(-1px);
}

/* ================================
   Custom Badges
   ================================ */
.cw-badge-success {
    background-color: var(--cw-success);
    color: white;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: var(--cw-border-radius-sm);
}

.cw-badge-warning {
    background-color: var(--cw-warning);
    color: white;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: var(--cw-border-radius-sm);
}

.cw-badge-danger {
    background-color: var(--cw-danger);
    color: white;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: var(--cw-border-radius-sm);
}

/* ================================
   Booking Card Styles
   ================================ */
.cw-card-booking {
    position: relative;
    overflow: hidden;
}

.cw-card-booking::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--cw-gray-300);
    transition: var(--cw-transition);
}

.cw-card-booking.available::before {
    background-color: var(--cw-success);
}

.cw-card-booking.pending::before {
    background-color: var(--cw-warning);
}

.cw-card-booking.confirmed::before {
    background-color: var(--cw-primary);
}

.cw-card-booking .card-body {
    padding-left: 1.75rem;
}

/* ================================
   Calendar Card Styles
   ================================ */
.cw-card-calendar {
    text-align: center;
    border: 2px solid var(--cw-gray-200);
    transition: var(--cw-transition);
}

.cw-card-calendar:hover {
    border-color: var(--cw-primary);
    box-shadow: var(--cw-shadow-lg);
}

.cw-calendar-day {
    margin-bottom: 1rem;
}

.cw-day-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--cw-primary);
    line-height: 1;
}

.cw-day-name {
    font-size: 0.875rem;
    color: var(--cw-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.cw-calendar-events {
    border-top: 1px solid var(--cw-gray-200);
    padding-top: 1rem;
}

.cw-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--cw-gray-100);
    font-size: 0.875rem;
}

.cw-event:last-child {
    border-bottom: none;
}

.cw-event-time {
    font-weight: 500;
    color: var(--cw-gray-600);
}

.cw-event-title {
    color: var(--cw-gray-700);
}

.cw-event-primary .cw-event-time {
    color: var(--cw-primary);
}

.cw-event-success .cw-event-time {
    color: var(--cw-success);
}

/* ================================
   Calendar Month View
   ================================ */
.cw-card-calendar-month {
    border: 1px solid var(--cw-gray-200);
}

.cw-calendar-mini {
    font-size: 0.875rem;
}

.cw-calendar-header {
    font-weight: 600;
    color: var(--cw-gray-700);
    padding: 0.5rem 0;
    border-bottom: 2px solid var(--cw-gray-200);
    margin-bottom: 0.5rem;
}

.cw-calendar-cell {
    padding: 0.75rem 0.25rem;
    cursor: pointer;
    transition: var(--cw-transition);
    border-radius: var(--cw-border-radius-sm);
    margin: 1px;
}

.cw-calendar-cell:hover {
    background-color: var(--cw-primary-light);
    color: white;
}

.cw-calendar-today {
    background-color: var(--cw-primary);
    color: white;
    font-weight: 600;
}

/* ================================
   Form Card Styles
   ================================ */
.cw-card-form {
    border: 1px solid var(--cw-gray-200);
}

.cw-card-form .form-control {
    border: 1px solid var(--cw-gray-300);
    border-radius: var(--cw-border-radius-sm);
    transition: var(--cw-transition);
}

.cw-card-form .form-control:focus {
    border-color: var(--cw-primary);
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.1);
}

.cw-card-form .form-label {
    font-weight: 500;
    color: var(--cw-gray-700);
    margin-bottom: 0.5rem;
}

/* ================================
   Statistics Card Styles
   ================================ */
.cw-card-stat {
    border: 1px solid var(--cw-gray-200);
    transition: var(--cw-transition);
}

.cw-card-stat:hover {
    box-shadow: var(--cw-shadow-lg);
    transform: translateY(-2px);
}

.cw-stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--cw-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.cw-card-stat .card-title {
    font-size: 0.875rem;
    color: var(--cw-gray-600);
    font-weight: 500;
}

.cw-card-stat h3 {
    font-weight: 700;
    color: var(--cw-gray-800);
}

/* ================================
   Responsive Design
   ================================ */
@media (max-width: 768px) {
    .cw-card {
        margin-bottom: 1rem;
    }
    
    .cw-calendar-mini {
        font-size: 0.75rem;
    }
    
    .cw-calendar-cell {
        padding: 0.5rem 0.125rem;
    }
    
    .cw-day-number {
        font-size: 1.5rem;
    }
    
    .cw-stat-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}

/* ================================
   Dark Mode Support (Optional)
   ================================ */
@media (prefers-color-scheme: dark) {
    .cw-card {
        background-color: var(--cw-gray-800);
        border-color: var(--cw-gray-700);
        color: var(--cw-gray-100);
    }
    
    .cw-card-header {
        background-color: var(--cw-gray-700);
        border-color: var(--cw-gray-600);
    }
    
    .cw-card-header h6 {
        color: var(--cw-gray-200);
    }
    
    .cw-calendar-header {
        color: var(--cw-gray-200);
        border-color: var(--cw-gray-600);
    }
    
    .cw-calendar-cell {
        color: var(--cw-gray-300);
    }
    
    .cw-event-time {
        color: var(--cw-gray-400);
    }
    
    .cw-event-title {
        color: var(--cw-gray-300);
    }
}

/* ================================
   Animation Classes
   ================================ */
.cw-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cw-slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ================================
   Utility Classes
   ================================ */
.cw-card-shadow-sm { box-shadow: var(--cw-shadow-sm); }
.cw-card-shadow { box-shadow: var(--cw-shadow); }
.cw-card-shadow-md { box-shadow: var(--cw-shadow-md); }
.cw-card-shadow-lg { box-shadow: var(--cw-shadow-lg); }
.cw-card-shadow-xl { box-shadow: var(--cw-shadow-xl); }

.cw-border-radius-sm { border-radius: var(--cw-border-radius-sm); }
.cw-border-radius { border-radius: var(--cw-border-radius); }
.cw-border-radius-lg { border-radius: var(--cw-border-radius-lg); } 