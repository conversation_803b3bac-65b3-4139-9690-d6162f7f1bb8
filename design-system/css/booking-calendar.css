/* Booking Calendar Components - CozyWish Design System */

:root {
  --cw-primary: #43251B;
  --cw-secondary: #5A342A;
  --cw-light: #FEF6F0;
  --cw-body-bg: #F8F9FA;
  --cw-body-color: #222222;
  --cw-border-color: #E0DFDE;
  --cw-success: #198754;
  --cw-warning: #ffc107;
  --cw-danger: #dc3545;
}

/* Base Card Styles */
.cw-booking-card {
  border: 1px solid var(--cw-border-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(67, 37, 27, 0.08);
  transition: all 0.3s ease;
  background: white;
}

.cw-booking-card:hover {
  box-shadow: 0 4px 16px rgba(67, 37, 27, 0.12);
  transform: translateY(-2px);
}

.cw-booking-card-header {
  background: var(--cw-light);
  border-bottom: 1px solid var(--cw-border-color);
  border-radius: 12px 12px 0 0;
  padding: 1rem 1.25rem;
}

.cw-booking-card-body {
  padding: 1.25rem;
}

/* Booking Status Cards */
.cw-booking-available {
  border-left: 4px solid var(--cw-success);
}

.cw-booking-pending {
  border-left: 4px solid var(--cw-warning);
}

.cw-booking-confirmed {
  border-left: 4px solid var(--cw-primary);
}

.cw-booking-cancelled {
  border-left: 4px solid var(--cw-danger);
}

/* Booking Info Items */
.cw-booking-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.cw-booking-info:last-child {
  margin-bottom: 0;
}

.cw-booking-info .bi {
  margin-right: 0.5rem;
  text-align: center;
}

.cw-booking-info.primary .bi {
  color: var(--cw-primary);
}

.cw-booking-info.success .bi {
  color: var(--cw-success);
}

.cw-booking-info.warning .bi {
  color: var(--cw-warning);
}

/* Calendar Components */
.cw-calendar-card {
  border: 1px solid var(--cw-border-color);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.cw-calendar-day {
  text-align: center;
  padding: 1rem 0;
}

.cw-day-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cw-primary);
  line-height: 1;
}

.cw-day-name {
  font-size: 0.875rem;
  color: var(--cw-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.25rem;
}

.cw-calendar-events {
  padding: 0 1rem 1rem;
}

.cw-event {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.cw-event:last-child {
  margin-bottom: 0;
}

.cw-event-primary {
  background: rgba(67, 37, 27, 0.1);
  border-left: 3px solid var(--cw-primary);
}

.cw-event-success {
  background: rgba(25, 135, 84, 0.1);
  border-left: 3px solid var(--cw-success);
}

.cw-event-time {
  font-weight: 600;
  color: var(--cw-body-color);
}

.cw-event-title {
  color: var(--cw-secondary);
}

/* Mini Calendar */
.cw-calendar-mini {
  font-size: 0.875rem;
}

.cw-calendar-header {
  font-weight: 600;
  color: var(--cw-primary);
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--cw-border-color);
  margin-bottom: 0.5rem;
}

.cw-calendar-cell {
  padding: 0.5rem 0;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.cw-calendar-cell:hover {
  background: var(--cw-light);
}

.cw-calendar-today {
  background: var(--cw-primary);
  color: white;
  font-weight: 600;
}

.cw-calendar-today:hover {
  background: var(--cw-secondary);
}

/* Statistics Cards */
.cw-stat-card {
  border: 1px solid var(--cw-border-color);
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
}

.cw-stat-card:hover {
  box-shadow: 0 4px 16px rgba(67, 37, 27, 0.12);
}

.cw-stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.cw-stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--cw-primary);
  line-height: 1;
  margin-bottom: 0;
}

.cw-stat-label {
  font-size: 0.875rem;
  color: var(--cw-secondary);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

/* Custom Badges */
.cw-badge-primary {
  background-color: var(--cw-primary);
}

.cw-badge-success {
  background-color: var(--cw-success);
}

.cw-badge-warning {
  background-color: var(--cw-warning);
  color: var(--cw-body-color);
}

.cw-badge-danger {
  background-color: var(--cw-danger);
}

/* Custom Buttons */
.cw-btn-primary {
  background-color: var(--cw-primary);
  border-color: var(--cw-primary);
  color: white;
}

.cw-btn-primary:hover {
  background-color: var(--cw-secondary);
  border-color: var(--cw-secondary);
  color: white;
}

.cw-btn-success {
  background-color: var(--cw-success);
  border-color: var(--cw-success);
}

/* Form Elements */
.cw-form-card {
  border: 1px solid var(--cw-border-color);
  border-radius: 12px;
  background: white;
}

.cw-form-card .form-control:focus {
  border-color: var(--cw-primary);
  box-shadow: 0 0 0 0.2rem rgba(67, 37, 27, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .cw-booking-card-body {
    padding: 1rem;
  }
  
  .cw-booking-card-header {
    padding: 0.75rem 1rem;
  }
  
  .cw-day-number {
    font-size: 1.5rem;
  }
  
  .cw-stat-number {
    font-size: 1.5rem;
  }
} 