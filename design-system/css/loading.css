/* CozyWish Brand-Specific Loading Components CSS */
/* Ensures all loading components use brand colors consistently */

/* ===== BRAND COLOR OVERRIDES FOR LOADING COMPONENTS ===== */
:root {
    /* Brand-specific loading colors */
    --loading-primary: #43251B;
    --loading-secondary: #5A342A;
    --loading-accent-light: #FEF6F0;
    --loading-background: #F8F9FA;
    --loading-border: #E0DFDE;
    --loading-text: #222222;
    
    /* Animation settings */
    --loading-animation-duration: 0.75s;
    --loading-border-radius: 0.5rem;
    --loading-shadow: 0 0.125rem 0.25rem rgba(67, 37, 27, 0.075);
    --loading-shadow-lg: 0 0.5rem 1rem rgba(67, 37, 27, 0.15);
    
    /* Skeleton loading colors */
    --skeleton-bg: linear-gradient(90deg, #F8F9FA 25%, #E0DFDE 50%, #F8F9FA 75%);
    --skeleton-animation: shimmer 1.5s infinite;
}

/* ===== SPINNER BRAND OVERRIDES ===== */
.spinner-border.text-primary,
.spinner-grow.text-primary {
    color: var(--loading-primary) !important;
}

.spinner-border.text-secondary,
.spinner-grow.text-secondary {
    color: var(--loading-secondary) !important;
}

/* Override any Bootstrap default blue spinners */
.spinner-border.text-info,
.spinner-grow.text-info {
    color: var(--loading-primary) !important;
}

/* ===== PROGRESS BAR BRAND OVERRIDES ===== */
.progress-enhanced .progress-bar.bg-primary {
    background-color: var(--loading-primary) !important;
}

.progress-enhanced .progress-bar.bg-info {
    background-color: var(--loading-primary) !important;
}

.progress-enhanced .progress-bar.bg-gradient {
    background: linear-gradient(45deg, var(--loading-primary) 0%, var(--loading-primary) 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, var(--loading-primary) 75%) !important;
}

/* ===== CIRCULAR PROGRESS BRAND OVERRIDES ===== */
.circular-progress-circle {
    background: conic-gradient(var(--loading-primary) calc(var(--progress) * 1%), #f1f3f4 0) !important;
}

.circular-progress-circle i {
    color: var(--loading-primary) !important;
}

.circular-progress-text {
    color: var(--loading-primary) !important;
}

/* ===== STEP PROGRESS BRAND OVERRIDES ===== */
.step-progress-item.completed .step-progress-number {
    background-color: var(--loading-primary) !important;
    color: #FFFFFF !important;
}

.step-progress-item.active .step-progress-number {
    background-color: var(--loading-secondary) !important;
    color: #FFFFFF !important;
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25) !important;
}

.step-progress-item.completed .step-progress-content h6,
.step-progress-item.active .step-progress-content h6 {
    color: var(--loading-primary) !important;
}

/* ===== SKELETON LOADER BRAND OVERRIDES ===== */
.skeleton-card-modern,
.skeleton-list-modern,
.skeleton-form-modern {
    background: #FFFFFF !important;
    border: 1px solid var(--loading-border) !important;
    box-shadow: var(--loading-shadow) !important;
}

.skeleton-title-modern,
.skeleton-text-modern,
.skeleton-text-short-modern,
.skeleton-button-modern,
.skeleton-button-outline-modern,
.skeleton-avatar-modern,
.skeleton-icon-modern,
.skeleton-label-modern,
.skeleton-input-modern,
.skeleton-textarea-modern {
    background: var(--skeleton-bg) !important;
    animation: var(--skeleton-animation) !important;
}

/* ===== LOADING STATE BRAND OVERRIDES ===== */
.page-loading-modern {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
}

.page-loading-content h6 {
    color: var(--loading-primary) !important;
}

.content-loading-overlay {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(5px) !important;
}

/* ===== UPLOAD AREA BRAND OVERRIDES ===== */
.upload-area-modern {
    border: 2px dashed var(--loading-border) !important;
    background: var(--loading-background) !important;
    transition: all 0.3s ease !important;
}

.upload-area-modern:hover {
    border-color: var(--loading-primary) !important;
    background: var(--loading-accent-light) !important;
}

.upload-area-modern::before {
    background: linear-gradient(45deg, transparent 30%, rgba(67, 37, 27, 0.1) 50%, transparent 70%) !important;
}

.upload-area-content h6 {
    color: var(--loading-primary) !important;
}

.upload-area-modern i {
    color: var(--loading-primary) !important;
}

/* ===== BUTTON LOADING BRAND OVERRIDES ===== */
.btn-loading::after {
    border-color: transparent !important;
    border-top-color: #FFFFFF !important;
}

/* ===== SEARCH LOADING BRAND OVERRIDES ===== */
.search-loading-demo .input-group-text {
    background-color: var(--loading-accent-light) !important;
    border-color: var(--loading-border) !important;
    color: var(--loading-primary) !important;
}

.search-results-loading {
    background: #FFFFFF !important;
    border: 1px solid var(--loading-border) !important;
}

/* ===== ALERT BRAND OVERRIDES ===== */
.alert-info {
    background-color: rgba(67, 37, 27, 0.1) !important;
    border-color: rgba(67, 37, 27, 0.2) !important;
    color: var(--loading-primary) !important;
}

.alert-info i {
    color: var(--loading-primary) !important;
}

/* ===== CARD BRAND OVERRIDES ===== */
.card.border-success {
    border-color: var(--loading-primary) !important;
}

.card-header.bg-success.bg-opacity-10 {
    background-color: var(--loading-accent-light) !important;
}

.card-header h6.text-success {
    color: var(--loading-primary) !important;
}

/* ===== ICON BRAND OVERRIDES ===== */
.bi.text-info {
    color: var(--loading-primary) !important;
}

/* ===== FOCUS STATES BRAND OVERRIDES ===== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    border-color: var(--loading-primary) !important;
    box-shadow: 0 0 0 0.25rem rgba(67, 37, 27, 0.25) !important;
}

/* ===== HOVER STATES BRAND OVERRIDES ===== */
.btn:hover {
    background-color: var(--loading-secondary) !important;
    border-color: var(--loading-secondary) !important;
}

/* ===== RESPONSIVE BRAND OVERRIDES ===== */
@media (max-width: 768px) {
    .loading-demo-grid {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)) !important;
    }
    
    .circular-progress-circle {
        width: 60px !important;
        height: 60px !important;
    }
    
    .circular-progress-circle::before {
        width: 44px !important;
        height: 44px !important;
    }
}

/* ===== ACCESSIBILITY BRAND OVERRIDES ===== */
@media (prefers-reduced-motion: reduce) {
    .spinner-border,
    .spinner-grow,
    .spinner-icon {
        animation-duration: 0.1s !important;
    }
    
    .progress-enhanced .progress-bar.bg-gradient::before {
        animation: none !important;
    }
    
    .step-progress-item.active .step-progress-number {
        animation: none !important;
    }
    
    .upload-area-modern::before {
        animation: none !important;
    }
}

/* ===== DARK MODE BRAND OVERRIDES ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --loading-background: #1a1a1a;
        --loading-border: #333333;
        --loading-text: #ffffff;
    }
    
    .skeleton-card-modern,
    .skeleton-list-modern,
    .skeleton-form-modern {
        background: #2d2d2d !important;
        border-color: #404040 !important;
    }
    
    .content-placeholder {
        background: #2d2d2d !important;
        border-color: #404040 !important;
    }
    
    .upload-area-modern {
        background: #2d2d2d !important;
        border-color: #404040 !important;
    }
}

/* ===== HIGH CONTRAST BRAND OVERRIDES ===== */
@media (prefers-contrast: high) {
    .skeleton-title-modern,
    .skeleton-text-modern,
    .skeleton-text-short-modern {
        background: #000000 !important;
    }
    
    .progress-enhanced .progress-bar {
        border: 2px solid var(--loading-primary) !important;
    }
    
    .circular-progress-circle {
        border: 3px solid var(--loading-primary) !important;
    }
} 