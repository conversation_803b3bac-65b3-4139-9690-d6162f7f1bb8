/* CozyWish Alerts & Notifications Component Styles */

/* <PERSON><PERSON> Component Styles - Using CozyWish Brand Colors */
.alert {
    border-radius: 0.375rem;
    border: 1px solid transparent;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.alert-success {
    color: var(--bs-success);
    background-color: var(--bs-success-light);
    border-color: var(--bs-success);
}

.alert-danger {
    color: var(--bs-danger);
    background-color: var(--bs-danger-light);
    border-color: var(--bs-danger);
}

.alert-warning {
    color: var(--bs-warning);
    background-color: var(--bs-warning-light);
    border-color: var(--bs-warning);
}

.alert-info {
    color: var(--bs-info);
    background-color: var(--bs-info-light);
    border-color: var(--bs-info);
}

.alert-primary {
    color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-color: var(--bs-primary);
}

.alert-secondary {
    color: var(--bs-secondary);
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
    border-color: var(--bs-secondary);
}

.alert-light {
    color: #636464;
    background-color: #fefefe;
    border-color: #fdfdfe;
}

.alert-dark {
    color: #141619;
    background-color: #d3d3d4;
    border-color: #bcbebf;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 0.75rem 1rem;
}

.alert-heading {
    color: inherit;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.alert-link {
    font-weight: 600;
    text-decoration: underline;
}

.alert-link:hover {
    text-decoration: none;
}

/* Toast Notification Styles */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1055;
}

.toast {
    border-radius: 0.375rem;
    border: 1px solid transparent;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 0.75rem;
    min-width: 300px;
    max-width: 400px;
}

.toast-success {
    color: var(--bs-success);
    background-color: var(--bs-success-light);
    border-color: var(--bs-success);
}

.toast-danger {
    color: var(--bs-danger);
    background-color: var(--bs-danger-light);
    border-color: var(--bs-danger);
}

.toast-warning {
    color: var(--bs-warning);
    background-color: var(--bs-warning-light);
    border-color: var(--bs-warning);
}

.toast-info {
    color: var(--bs-info);
    background-color: var(--bs-info-light);
    border-color: var(--bs-info);
}

.toast-header {
    background-color: rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.5rem 0.75rem;
}

.toast-body {
    padding: 0.75rem;
    color: inherit;
}

/* Modal Styles */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid var(--bs-border-color);
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1rem 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--bs-border-color);
    padding: 0.75rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: var(--bs-heading-color);
}

/* Component Demo Styles */
.component-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.component-title {
    color: var(--bs-primary);
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--bs-light);
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.demo-item {
    padding: 1.5rem;
    background: #fff;
    border-radius: 0.375rem;
    border: 1px solid var(--bs-border-color);
}

.demo-item h6 {
    color: var(--bs-primary);
    font-weight: 600;
    margin-bottom: 1rem;
}

.btn-demo {
    margin: 0.25rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .demo-grid {
        grid-template-columns: 1fr;
    }
    
    .toast {
        min-width: 280px;
        max-width: 320px;
    }
    
    .modal-content {
        margin: 1rem;
    }
} 