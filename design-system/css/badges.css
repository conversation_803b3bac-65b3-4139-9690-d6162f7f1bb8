/* ===== COZYWISH BADGE COMPONENT STYLES ===== */
/* Professional badge system with Bootstrap 5 integration */

:root {
    /* Badge-specific color variables */
    --badge-primary: #43251B;
    --badge-primary-light: rgba(67, 37, 27, 0.1);
    --badge-secondary: #5A342A;
    --badge-secondary-light: rgba(90, 52, 42, 0.1);
    --badge-success: #2D5A3D;
    --badge-success-light: rgba(45, 90, 61, 0.1);
    --badge-warning: #B8860B;
    --badge-warning-light: rgba(184, 134, 11, 0.1);
    --badge-danger: #8B2635;
    --badge-danger-light: rgba(139, 38, 53, 0.1);
    --badge-info: #2C5F8A;
    --badge-info-light: rgba(44, 95, 138, 0.1);
    --badge-light: #FEF6F0;
    --badge-dark: #222222;
    
    /* Badge sizing variables */
    --badge-font-size-sm: 0.625rem;
    --badge-font-size: 0.75rem;
    --badge-font-size-lg: 0.875rem;
    --badge-font-size-xl: 1rem;
    
    --badge-padding-sm: 0.25rem 0.5rem;
    --badge-padding: 0.375rem 0.75rem;
    --badge-padding-lg: 0.5rem 1rem;
    --badge-padding-xl: 0.625rem 1.25rem;
    
    --badge-border-radius-sm: 0.25rem;
    --badge-border-radius: 0.375rem;
    --badge-border-radius-lg: 0.5rem;
    --badge-border-radius-xl: 0.625rem;
    
    /* Animation variables */
    --badge-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --badge-hover-transform: translateY(-1px);
    --badge-active-transform: translateY(0);
    
    /* Shadow variables */
    --badge-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
    --badge-shadow-active: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== BASE BADGE STYLES ===== */
.badge {
    font-weight: 500 !important;
    font-size: var(--badge-font-size) !important;
    line-height: 1.2 !important;
    padding: var(--badge-padding) !important;
    border-radius: var(--badge-border-radius) !important;
    transition: var(--badge-transition) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    vertical-align: baseline !important;
    border: 1px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Badge icon styling */
.badge i {
    font-size: 0.875em !important;
    line-height: 1 !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* ===== BADGE SIZES ===== */
.badge-sm {
    font-size: var(--badge-font-size-sm) !important;
    padding: var(--badge-padding-sm) !important;
    border-radius: var(--badge-border-radius-sm) !important;
}

.badge-sm i {
    font-size: 0.75em !important;
}

.badge-lg {
    font-size: var(--badge-font-size-lg) !important;
    padding: var(--badge-padding-lg) !important;
    border-radius: var(--badge-border-radius-lg) !important;
}

.badge-lg i {
    font-size: 1em !important;
}

.badge-xl {
    font-size: var(--badge-font-size-xl) !important;
    padding: var(--badge-padding-xl) !important;
    border-radius: var(--badge-border-radius-xl) !important;
}

.badge-xl i {
    font-size: 1.125em !important;
}

/* ===== PILL BADGES ===== */
.badge.rounded-pill {
    border-radius: 50rem !important;
}

/* ===== COLOR VARIANTS ===== */

/* Primary badges */
.badge.bg-primary {
    background-color: var(--badge-primary) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-primary) !important;
}

.badge.bg-primary-light {
    background-color: var(--badge-primary-light) !important;
    color: var(--badge-primary) !important;
    border-color: var(--badge-primary-light) !important;
}

/* Secondary badges */
.badge.bg-secondary {
    background-color: var(--badge-secondary) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-secondary) !important;
}

.badge.bg-secondary-light {
    background-color: var(--badge-secondary-light) !important;
    color: var(--badge-secondary) !important;
    border-color: var(--badge-secondary-light) !important;
}

/* Success badges */
.badge.bg-success {
    background-color: var(--badge-success) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-success) !important;
}

.badge.bg-success-light {
    background-color: var(--badge-success-light) !important;
    color: var(--badge-success) !important;
    border-color: var(--badge-success-light) !important;
}

/* Warning badges */
.badge.bg-warning {
    background-color: var(--badge-warning) !important;
    color: #000000 !important;
    border-color: var(--badge-warning) !important;
}

.badge.bg-warning-light {
    background-color: var(--badge-warning-light) !important;
    color: var(--badge-warning) !important;
    border-color: var(--badge-warning-light) !important;
}

/* Danger badges */
.badge.bg-danger {
    background-color: var(--badge-danger) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-danger) !important;
}

.badge.bg-danger-light {
    background-color: var(--badge-danger-light) !important;
    color: var(--badge-danger) !important;
    border-color: var(--badge-danger-light) !important;
}

/* Info badges */
.badge.bg-info {
    background-color: var(--badge-info) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-info) !important;
}

.badge.bg-info-light {
    background-color: var(--badge-info-light) !important;
    color: var(--badge-info) !important;
    border-color: var(--badge-info-light) !important;
}

/* Light badges */
.badge.bg-light {
    background-color: var(--badge-light) !important;
    color: #000000 !important;
    border-color: var(--badge-light) !important;
}

/* Dark badges */
.badge.bg-dark {
    background-color: var(--badge-dark) !important;
    color: #FFFFFF !important;
    border-color: var(--badge-dark) !important;
}

/* ===== OUTLINE BADGES ===== */
.badge.badge-outline {
    background-color: transparent !important;
    border-width: 2px !important;
}

.badge.badge-outline.bg-primary {
    color: var(--badge-primary) !important;
    border-color: var(--badge-primary) !important;
}

.badge.badge-outline.bg-secondary {
    color: var(--badge-secondary) !important;
    border-color: var(--badge-secondary) !important;
}

.badge.badge-outline.bg-success {
    color: var(--badge-success) !important;
    border-color: var(--badge-success) !important;
}

.badge.badge-outline.bg-warning {
    color: var(--badge-warning) !important;
    border-color: var(--badge-warning) !important;
}

.badge.badge-outline.bg-danger {
    color: var(--badge-danger) !important;
    border-color: var(--badge-danger) !important;
}

.badge.badge-outline.bg-info {
    color: var(--badge-info) !important;
    border-color: var(--badge-info) !important;
}

/* ===== SPECIAL BADGE STYLES ===== */

/* Premium badges */
.badge-premium {
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    background: linear-gradient(135deg, var(--badge-primary) 0%, #5A342A 100%) !important;
    position: relative !important;
}

.badge-premium::before {
    content: '';
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent) !important;
    transition: left 0.5s ease-in-out !important;
}

.badge-premium:hover::before {
    left: 100% !important;
}

/* Verified badges */
.badge-verified {
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    position: relative !important;
}

/* New badges */
.badge-new {
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    animation: badgePulse 2s infinite ease-in-out;
}

/* ===== INTERACTIVE BADGES ===== */
.badge.border-0 {
    border: none !important;
    cursor: pointer !important;
    user-select: none !important;
}

.badge.border-0:hover {
    transform: var(--badge-hover-transform) !important;
    box-shadow: var(--badge-shadow-hover) !important;
}

.badge.border-0:active {
    transform: var(--badge-active-transform) !important;
    box-shadow: var(--badge-shadow-active) !important;
}

.badge.border-0:focus {
    outline: 2px solid var(--badge-primary) !important;
    outline-offset: 2px !important;
}

/* Filter badges */
.badge-filter {
    transition: var(--badge-transition) !important;
    cursor: pointer !important;
}

.badge-filter:not(.active):hover {
    background-color: var(--badge-primary-light) !important;
    color: var(--badge-primary) !important;
}

.badge-filter.active {
    background-color: var(--badge-primary) !important;
    color: #FFFFFF !important;
    box-shadow: var(--badge-shadow-active) !important;
}

/* ===== REMOVABLE BADGES ===== */
.badge-removable {
    padding-right: 0.5rem !important;
    position: relative !important;
}

.badge-remove {
    background: none !important;
    border: none !important;
    color: inherit !important;
    font-size: 0.875em !important;
    margin-left: 0.375rem !important;
    padding: 0 !important;
    cursor: pointer !important;
    opacity: 0.7 !important;
    transition: var(--badge-transition) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    width: 1.2em !important;
    height: 1.2em !important;
}

.badge-remove:hover {
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.badge-remove:focus {
    outline: 1px solid currentColor !important;
    outline-offset: 1px !important;
}

/* ===== BADGE SHOWCASE STYLING ===== */
.badge-showcase {
    padding: 0.5rem 0 !important;
}

.badge-showcase .badge {
    margin-bottom: 0.5rem !important;
}

/* ===== EXAMPLE CARD STYLING ===== */
.example-card {
    background-color: #FAFAFA !important;
    transition: var(--badge-transition) !important;
}

.example-card:hover {
    background-color: #F5F5F5 !important;
}

.notification-item {
    border-color: rgba(0, 0, 0, 0.1) !important;
}

.notification-item:last-child {
    border-bottom: none !important;
}

/* ===== BADGE ANIMATIONS ===== */
@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

@keyframes badgeShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.badge-pulse {
    animation: badgePulse 2s infinite ease-in-out;
}

.badge-shimmer {
    background: linear-gradient(90deg, currentColor 25%, transparent 50%, currentColor 75%);
    background-size: 200% 100%;
    animation: badgeShimmer 2s infinite;
}

/* ===== BADGE STATES ===== */
.badge:disabled,
.badge.disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.badge.loading {
    pointer-events: none !important;
}

.badge.loading::after {
    content: '';
    width: 0.75em !important;
    height: 0.75em !important;
    border: 2px solid transparent !important;
    border-top: 2px solid currentColor !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    margin-left: 0.375rem !important;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== BADGE GROUPS ===== */
.badge-group {
    display: inline-flex !important;
    flex-wrap: wrap !important;
    gap: 0.375rem !important;
    align-items: center !important;
}

.badge-group .badge {
    margin: 0 !important;
}

/* Connected badge group */
.badge-group.badge-group-connected {
    gap: 0 !important;
}

.badge-group.badge-group-connected .badge {
    border-radius: 0 !important;
    border-right-width: 0 !important;
}

.badge-group.badge-group-connected .badge:first-child {
    border-top-left-radius: var(--badge-border-radius) !important;
    border-bottom-left-radius: var(--badge-border-radius) !important;
}

.badge-group.badge-group-connected .badge:last-child {
    border-top-right-radius: var(--badge-border-radius) !important;
    border-bottom-right-radius: var(--badge-border-radius) !important;
    border-right-width: 1px !important;
}

/* ===== BADGE COUNTERS ===== */
.badge-counter {
    position: relative !important;
}

.badge-counter::after {
    content: attr(data-count) !important;
    position: absolute !important;
    top: -0.5rem !important;
    right: -0.5rem !important;
    background-color: var(--badge-danger) !important;
    color: #FFFFFF !important;
    border-radius: 50% !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    font-size: 0.625rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    border: 2px solid #FFFFFF !important;
    box-shadow: var(--badge-shadow-active) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .badge {
        font-size: 0.7rem !important;
        padding: 0.3rem 0.6rem !important;
    }
    
    .badge-sm {
        font-size: 0.6rem !important;
        padding: 0.2rem 0.4rem !important;
    }
    
    .badge-lg {
        font-size: 0.8rem !important;
        padding: 0.4rem 0.8rem !important;
    }
    
    .badge-xl {
        font-size: 0.9rem !important;
        padding: 0.5rem 1rem !important;
    }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --badge-light: #2A2A2A;
        --badge-dark: #F5F5F5;
    }
    
    .badge.bg-light {
        background-color: #2A2A2A !important;
        color: #FFFFFF !important;
    }
    
    .badge.bg-dark {
        background-color: #F5F5F5 !important;
        color: #000000 !important;
    }
    
    .example-card {
        background-color: #1A1A1A !important;
        border-color: #333333 !important;
    }
    
    .example-card:hover {
        background-color: #252525 !important;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .badge,
    .badge::before,
    .badge::after,
    .badge-remove {
        transition: none !important;
        animation: none !important;
    }
    
    .badge-pulse,
    .badge-shimmer,
    .badge-new {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .badge {
        border-width: 2px !important;
    }
    
    .badge-outline {
        border-width: 3px !important;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .badge {
        border: 1px solid #000000 !important;
        background-color: #FFFFFF !important;
        color: #000000 !important;
        box-shadow: none !important;
    }
    
    .badge.bg-success,
    .badge.bg-danger,
    .badge.bg-warning,
    .badge.bg-info,
    .badge.bg-primary,
    .badge.bg-secondary {
        background-color: #FFFFFF !important;
        color: #000000 !important;
        border-color: #000000 !important;
    }
    
    .badge::before,
    .badge::after {
        display: none !important;
    }
} 