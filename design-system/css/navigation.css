/* Store Navigation Components - CozyWish Design System */

/* ==========================================================================
   Page Layout
   ========================================================================== */

.page-header {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 1rem 1rem;
}

.header-content {
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: var(--bs-font-weight-bold);
    margin-bottom: 1rem;
    color: white;
}

.page-description {
    font-size: 1.125rem;
    margin-bottom: 0;
    opacity: 0.9;
}

.component-section {
    margin-bottom: 4rem;
}

.section-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.section-title {
    font-size: 2rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.125rem;
    color: #6c757d;
    margin-bottom: 0;
}

.component-demo {
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.75rem;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.demo-header {
    background: var(--bs-light);
    padding: 1.5rem;
    border-bottom: 1px solid var(--bs-border-color);
}

.demo-title {
    font-size: 1.25rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 0.5rem;
}

.demo-description {
    color: #6c757d;
    margin-bottom: 0;
}

.demo-content {
    padding: 2rem;
}

/* ==========================================================================
   Category Navigation
   ========================================================================== */

.category-nav {
    background: white;
    border-radius: 0.5rem;
    border: 1px solid var(--bs-border-color);
    overflow: hidden;
}

.category-tabs {
    display: flex;
    background: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.category-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: var(--bs-body-color);
    font-weight: var(--bs-font-weight-normal);
    white-space: nowrap;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
}

.category-tab:hover {
    background: rgba(67, 37, 27, 0.05);
    color: var(--bs-primary);
}

.category-tab.active {
    background: white;
    color: var(--bs-primary);
    font-weight: var(--bs-font-weight-bold);
}

.category-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--bs-primary);
}

.category-icon {
    font-size: 1.125rem;
}

.category-label {
    font-size: 0.875rem;
}

.category-badge {
    background: #dc3545;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-weight: var(--bs-font-weight-bold);
}

.category-content {
    padding: 2rem;
}

.category-panel {
    display: none;
}

.category-panel.active {
    display: block;
}

.panel-content {
    text-align: center;
}

.panel-title {
    font-size: 1.5rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 1rem;
}

.panel-description {
    color: #6c757d;
    margin-bottom: 2rem;
}

.panel-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--bs-light);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: var(--bs-font-weight-normal);
}

.stat-icon {
    color: var(--bs-primary);
    font-size: 1rem;
}

/* ==========================================================================
   Filter Navigation
   ========================================================================== */

.filter-navigation {
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    overflow: hidden;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--bs-light);
    border-bottom: 1px solid var(--bs-border-color);
}

.filter-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 0;
}

.filter-clear {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    border: none;
    color: #dc3545;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.filter-clear:hover {
    background: rgba(220, 53, 69, 0.1);
}

.filter-groups {
    padding: 1.5rem;
    display: grid;
    gap: 2rem;
}

.filter-group {
    border-bottom: 1px solid var(--bs-border-color);
    padding-bottom: 1.5rem;
}

.filter-group:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.filter-group-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 1rem;
}

.filter-options {
    display: grid;
    gap: 0.5rem;
}

.filter-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: transparent;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    color: var(--bs-body-color);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.filter-option:hover {
    border-color: var(--bs-primary);
    background: rgba(67, 37, 27, 0.05);
}

.filter-option.active {
    background: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.filter-label {
    font-weight: var(--bs-font-weight-normal);
}

.filter-count {
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 0.75rem;
    font-weight: var(--bs-font-weight-bold);
}

.filter-option.active .filter-count {
    background: rgba(255, 255, 255, 0.3);
}

/* ==========================================================================
   List Controls
   ========================================================================== */

.list-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem;
    flex-wrap: wrap;
}

.list-info {
    flex: 1;
}

.results-summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--bs-body-color);
    font-size: 0.875rem;
}

.results-summary i {
    color: var(--bs-primary);
    font-size: 1rem;
}

.list-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sort-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--bs-body-color);
    margin-bottom: 0;
}

.sort-label i {
    color: var(--bs-primary);
}

.sort-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    background: white;
    color: var(--bs-body-color);
    font-size: 0.875rem;
}

.sort-select:focus {
    outline: none;
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

.view-control {
    display: flex;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    overflow: hidden;
}

.view-option {
    padding: 0.5rem 0.75rem;
    background: white;
    border: none;
    color: var(--bs-body-color);
    transition: all 0.2s ease;
    cursor: pointer;
}

.view-option:hover {
    background: var(--bs-light);
}

.view-option.active {
    background: var(--bs-primary);
    color: white;
}

/* ==========================================================================
   Pagination
   ========================================================================== */

.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.pagination-nav {
    flex: 1;
}

.pagination-list {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: center;
}

.pagination-item {
    margin: 0;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0.5rem 0.75rem;
    background: white;
    border: 1px solid var(--bs-border-color);
    color: var(--bs-body-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: var(--bs-font-weight-normal);
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.pagination-link:hover:not(.disabled) {
    background: var(--bs-light);
    border-color: var(--bs-primary);
    color: var(--bs-primary);
}

.pagination-link.active {
    background: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
    font-weight: var(--bs-font-weight-bold);
}

.pagination-link.disabled {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    cursor: not-allowed;
}

.pagination-prev,
.pagination-next {
    gap: 0.5rem;
}

.pagination-text {
    font-size: 0.875rem;
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-size-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-size-label {
    font-size: 0.875rem;
    color: var(--bs-body-color);
    margin-bottom: 0;
    white-space: nowrap;
}

.page-size-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    background: white;
    color: var(--bs-body-color);
    font-size: 0.875rem;
    min-width: 120px;
}

.page-size-select:focus {
    outline: none;
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.125rem rgba(67, 37, 27, 0.25);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .demo-content {
        padding: 1rem;
    }
    
    .category-tabs {
        border-radius: 0;
    }
    
    .category-tab {
        padding: 0.75rem 1rem;
        min-width: 120px;
    }
    
    .panel-stats {
        gap: 1rem;
    }
    
    .stat-item {
        font-size: 0.75rem;
    }
    
    .filter-groups {
        padding: 1rem;
        gap: 1.5rem;
    }
    
    .list-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .list-actions {
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .pagination-wrapper {
        flex-direction: column;
        gap: 1rem;
    }
    
    .pagination-nav {
        width: 100%;
    }
    
    .pagination-list {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination-text {
        display: none;
    }
    
    .pagination-prev,
    .pagination-next {
        min-width: 2.5rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.75rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .category-tab {
        padding: 0.5rem 0.75rem;
        min-width: 100px;
    }
    
    .category-label {
        font-size: 0.75rem;
    }
    
    .filter-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .pagination-link {
        min-width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }
}

/* ==========================================================================
   Breadcrumb Navigation
   ========================================================================== */

/* Basic Breadcrumb Styling */
.breadcrumb {
    background: transparent;
    padding: 0.75rem 0;
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
    font-weight: 600;
    margin: 0 0.5rem;
}

.breadcrumb-item a {
    color: var(--bs-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: var(--bs-secondary);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #6c757d;
    font-weight: 500;
}




/* Demo specific styles */
.demo-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.demo-title {
    font-size: 1.25rem;
    font-weight: var(--bs-font-weight-bold);
    color: var(--bs-heading-color);
    margin-bottom: 0.5rem;
}

.demo-description {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
}

/* Breadcrumb Responsive Design */
@media (min-width: 768px) {
    .breadcrumb {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        padding: 0.5rem 0;
        font-size: 0.875rem;
    }
} 