<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert & Notification Components - CozyWish Design System</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-primary mb-4">
                    <i class="fas fa-bell me-3"></i>
                    Alert & Notification Components
                </h1>
                <p class="lead text-muted mb-5">
                    Comprehensive alert and notification system for CozyWish applications
                </p>
            </div>
        </div>

        <!-- Alert Types Section -->
        <div class="component-section">
            <h2 class="component-title">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Alert Types
            </h2>
            
            <div class="demo-grid">
                <!-- Success Alerts -->
                <div class="demo-item">
                    <h6>Success Alerts</h6>
                    <div class="alert alert-success fade-in">
                        <strong>Success!</strong> Your booking has been confirmed successfully.
                    </div>
                    <div class="alert alert-success alert-dismissible fade-in">
                        <strong>Payment Complete!</strong> Your transaction has been processed.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <!-- Error Alerts -->
                <div class="demo-item">
                    <h6>Error Alerts</h6>
                    <div class="alert alert-danger fade-in">
                        <strong>Error!</strong> Unable to process your request. Please try again.
                    </div>
                    <div class="alert alert-danger alert-dismissible fade-in">
                        <strong>Connection Failed!</strong> Please check your internet connection.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <!-- Warning Alerts -->
                <div class="demo-item">
                    <h6>Warning Alerts</h6>
                    <div class="alert alert-warning fade-in">
                        <strong>Warning!</strong> Your session will expire in 5 minutes.
                    </div>
                    <div class="alert alert-warning alert-dismissible fade-in">
                        <strong>Low Balance!</strong> Please add funds to continue.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <!-- Info Alerts -->
                <div class="demo-item">
                    <h6>Info Alerts</h6>
                    <div class="alert alert-info fade-in">
                        <strong>Info!</strong> New features are available in the latest update.
                    </div>
                    <div class="alert alert-info alert-dismissible fade-in">
                        <strong>Maintenance!</strong> Scheduled maintenance on Sunday at 2 AM.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <!-- Primary Alerts -->
                <div class="demo-item">
                    <h6>Primary Alerts</h6>
                    <div class="alert alert-primary fade-in">
                        <strong>New Feature!</strong> Check out our new booking system.
                        <a href="#" class="alert-link">Learn more</a>
                    </div>
                    <div class="alert alert-primary alert-dismissible fade-in">
                        <strong>Update Available!</strong> A new version is ready to install.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <!-- Secondary Alerts -->
                <div class="demo-item">
                    <h6>Secondary Alerts</h6>
                    <div class="alert alert-secondary fade-in">
                        <strong>Secondary!</strong> This is a secondary alert message.
                    </div>
                    <div class="alert alert-secondary alert-dismissible fade-in">
                        <strong>Notice!</strong> This is a dismissible secondary alert.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notifications Section -->
        <div class="component-section">
            <h2 class="component-title">
                <i class="fas fa-toast me-2"></i>
                Toast Notifications
            </h2>
            
            <div class="demo-grid">
                <!-- Success Toasts -->
                <div class="demo-item">
                    <h6>Success Toasts</h6>
                    <button class="btn btn-success btn-demo" onclick="showToast('success', 'Booking confirmed successfully!')">
                        Show Success Toast
                    </button>
                </div>

                <!-- Error Toasts -->
                <div class="demo-item">
                    <h6>Error Toasts</h6>
                    <button class="btn btn-danger btn-demo" onclick="showToast('danger', 'Failed to process payment')">
                        Show Error Toast
                    </button>
                </div>

                <!-- Warning Toasts -->
                <div class="demo-item">
                    <h6>Warning Toasts</h6>
                    <button class="btn btn-warning btn-demo" onclick="showToast('warning', 'Session expires in 5 minutes')">
                        Show Warning Toast
                    </button>
                </div>

                <!-- Info Toasts -->
                <div class="demo-item">
                    <h6>Info Toasts</h6>
                    <button class="btn btn-info btn-demo" onclick="showToast('info', 'New message received')">
                        Show Info Toast
                    </button>
                </div>
            </div>

            <!-- Auto-dismiss Toast -->
            <div class="demo-item">
                <h6>Auto-dismiss Toast</h6>
                <button class="btn btn-primary btn-demo" onclick="showAutoDismissToast('success', 'Auto-dismissing in 3 seconds...')">
                    Show Auto-dismiss Toast
                </button>
            </div>
        </div>

        <!-- Modal Dialogs Section -->
        <div class="component-section">
            <h2 class="component-title">
                <i class="fas fa-window-maximize me-2"></i>
                Modal Dialogs
            </h2>
            
            <div class="demo-grid">
                <!-- Confirmation Modals -->
                <div class="demo-item">
                    <h6>Confirmation Modals</h6>
                    <button class="btn btn-danger btn-demo" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        Delete Confirmation
                    </button>
                    <button class="btn btn-warning btn-demo" data-bs-toggle="modal" data-bs-target="#logoutModal">
                        Logout Confirmation
                    </button>
                </div>

                <!-- Form Modals -->
                <div class="demo-item">
                    <h6>Form Modals</h6>
                    <button class="btn btn-primary btn-demo" data-bs-toggle="modal" data-bs-target="#bookingModal">
                        New Booking
                    </button>
                    <button class="btn btn-success btn-demo" data-bs-toggle="modal" data-bs-target="#contactModal">
                        Contact Form
                    </button>
                </div>

                <!-- Content Modals -->
                <div class="demo-item">
                    <h6>Content Modals</h6>
                    <button class="btn btn-info btn-demo" data-bs-toggle="modal" data-bs-target="#helpModal">
                        Help & Support
                    </button>
                    <button class="btn btn-secondary btn-demo" data-bs-toggle="modal" data-bs-target="#termsModal">
                        Terms & Conditions
                    </button>
                </div>

                <!-- Alert Modals -->
                <div class="demo-item">
                    <h6>Alert Modals</h6>
                    <button class="btn btn-warning btn-demo" data-bs-toggle="modal" data-bs-target="#maintenanceModal">
                        Maintenance Alert
                    </button>
                    <button class="btn btn-danger btn-demo" data-bs-toggle="modal" data-bs-target="#errorModal">
                        System Error
                    </button>
                </div>
            </div>
        </div>

        <!-- Toast Container -->
        <div class="toast-container" id="toastContainer"></div>

        <!-- Confirmation Modals -->
        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-trash-alt text-danger me-2"></i>
                            Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this item? This action cannot be undone.</p>
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> This will permanently remove the item from your account.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-2"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logout Confirmation Modal -->
        <div class="modal fade" id="logoutModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-sign-out-alt text-warning me-2"></i>
                            Confirm Logout
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to logout? Any unsaved changes will be lost.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Modals -->
        <!-- Booking Modal -->
        <div class="modal fade" id="bookingModal" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-calendar-plus text-primary me-2"></i>
                            New Booking
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="bookingDate" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="bookingDate" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="bookingTime" class="form-label">Time</label>
                                    <input type="time" class="form-control" id="bookingTime" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="bookingGuests" class="form-label">Number of Guests</label>
                                <select class="form-select" id="bookingGuests" required>
                                    <option value="">Select guests</option>
                                    <option value="1">1 Guest</option>
                                    <option value="2">2 Guests</option>
                                    <option value="3">3 Guests</option>
                                    <option value="4">4 Guests</option>
                                    <option value="5+">5+ Guests</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="bookingNotes" class="form-label">Special Requests</label>
                                <textarea class="form-control" id="bookingNotes" rows="3" placeholder="Any special requests or dietary requirements..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>Confirm Booking
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Modal -->
        <div class="modal fade" id="contactModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-envelope text-success me-2"></i>
                            Contact Us
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="contactName" class="form-label">Name</label>
                                <input type="text" class="form-control" id="contactName" required>
                            </div>
                            <div class="mb-3">
                                <label for="contactEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="contactEmail" required>
                            </div>
                            <div class="mb-3">
                                <label for="contactSubject" class="form-label">Subject</label>
                                <select class="form-select" id="contactSubject" required>
                                    <option value="">Select subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="booking">Booking Question</option>
                                    <option value="support">Technical Support</option>
                                    <option value="feedback">Feedback</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="contactMessage" class="form-label">Message</label>
                                <textarea class="form-control" id="contactMessage" rows="4" required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Modals -->
        <!-- Help Modal -->
        <div class="modal fade" id="helpModal" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-question-circle text-info me-2"></i>
                            Help & Support
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Frequently Asked Questions</h6>
                                <div class="accordion" id="helpAccordion">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                                How do I make a booking?
                                            </button>
                                        </h2>
                                        <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                            <div class="accordion-body">
                                                Click on the "New Booking" button and fill out the required information including date, time, and number of guests.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                                Can I cancel my booking?
                                            </button>
                                        </h2>
                                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
                                            <div class="accordion-body">
                                                Yes, you can cancel your booking up to 24 hours before the scheduled time through your account dashboard.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Contact Information</h6>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-phone text-primary me-3"></i>
                                    <div>
                                        <strong>Phone:</strong><br>
                                        +****************
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-envelope text-primary me-3"></i>
                                    <div>
                                        <strong>Email:</strong><br>
                                        <EMAIL>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock text-primary me-3"></i>
                                    <div>
                                        <strong>Hours:</strong><br>
                                        Mon-Fri: 9AM-6PM EST
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-info">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Terms Modal -->
        <div class="modal fade" id="termsModal" tabindex="-1">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-contract text-secondary me-2"></i>
                            Terms & Conditions
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <strong>Last updated:</strong> January 15, 2024
                        </div>
                        <h6>1. Acceptance of Terms</h6>
                        <p>By accessing and using CozyWish services, you accept and agree to be bound by the terms and provision of this agreement.</p>
                        
                        <h6>2. Use License</h6>
                        <p>Permission is granted to temporarily download one copy of the materials on CozyWish's website for personal, non-commercial transitory viewing only.</p>
                        
                        <h6>3. Disclaimer</h6>
                        <p>The materials on CozyWish's website are provided on an 'as is' basis. CozyWish makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>
                        
                        <h6>4. Limitations</h6>
                        <p>In no event shall CozyWish or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on CozyWish's website.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>I Accept
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Modals -->
        <!-- Maintenance Modal -->
        <div class="modal fade" id="maintenanceModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-tools text-warning me-2"></i>
                            Scheduled Maintenance
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <strong>Maintenance Notice:</strong> Our system will be undergoing scheduled maintenance.
                        </div>
                        <p><strong>Date:</strong> Sunday, January 21, 2024</p>
                        <p><strong>Time:</strong> 2:00 AM - 4:00 AM EST</p>
                        <p><strong>Impact:</strong> Service may be temporarily unavailable during this period.</p>
                        <p>We apologize for any inconvenience and appreciate your patience.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-warning" data-bs-dismiss="modal">
                            <i class="fas fa-bell me-2"></i>Remind Me Later
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" id="errorModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                            System Error
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <strong>Error Code:</strong> 500 - Internal Server Error
                        </div>
                        <p>We're experiencing technical difficulties. Our team has been notified and is working to resolve the issue.</p>
                        <p><strong>What you can do:</strong></p>
                        <ul>
                            <li>Try refreshing the page</li>
                            <li>Clear your browser cache</li>
                            <li>Contact support if the problem persists</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Toast Notification JavaScript -->
    <script>
        // Toast notification functions
        function showToast(type, message, title = null) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();
            
            let bgClass;
            switch(type) {
                case 'success':
                    bgClass = 'toast-success';
                    title = title || 'Success';
                    break;
                case 'danger':
                    bgClass = 'toast-danger';
                    title = title || 'Error';
                    break;
                case 'warning':
                    bgClass = 'toast-warning';
                    title = title || 'Warning';
                    break;
                case 'info':
                    bgClass = 'toast-info';
                    title = title || 'Info';
                    break;
                default:
                    bgClass = 'toast-info';
                    title = title || 'Info';
            }
            
            const toastHTML = `
                <div class="toast ${bgClass} slide-in" id="${toastId}" role="alert">
                    <div class="toast-header">
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }
        
        function showAutoDismissToast(type, message, title = null, delay = 3000) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();
            
            let bgClass;
            switch(type) {
                case 'success':
                    bgClass = 'toast-success';
                    title = title || 'Success';
                    break;
                case 'danger':
                    bgClass = 'toast-danger';
                    title = title || 'Error';
                    break;
                case 'warning':
                    bgClass = 'toast-warning';
                    title = title || 'Warning';
                    break;
                case 'info':
                    bgClass = 'toast-info';
                    title = title || 'Info';
                    break;
                default:
                    bgClass = 'toast-info';
                    title = title || 'Info';
            }
            
            const toastHTML = `
                <div class="toast ${bgClass} slide-in" id="${toastId}" role="alert">
                    <div class="toast-header">
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: delay
            });
            
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any Bootstrap components that need it
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html> 