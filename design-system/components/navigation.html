<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Navigation Components - CozyWish Design System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Product Category Navigation -->
        <section class="component-section">
            <div class="section-header">
                <h2 class="section-title">Product Category Navigation</h2>
                <p class="section-description">
                    Main navigation for product categories and store sections.
                </p>
            </div>

            <!-- Primary Category Tabs -->
            <div class="component-demo">
                <div class="demo-content">
                    <div class="category-nav">
                        <nav class="category-tabs" role="tablist">
                            <button class="category-tab active" role="tab" aria-selected="true" aria-controls="category-panel-1" id="category-1">
                                <i class="category-icon bi bi-house-door"></i>
                                <span class="category-label">Home Decor</span>
                            </button>
                            <button class="category-tab" role="tab" aria-selected="false" aria-controls="category-panel-2" id="category-2">
                                <i class="category-icon bi bi-lamp"></i>
                                <span class="category-label">Lighting</span>
                            </button>
                            <button class="category-tab" role="tab" aria-selected="false" aria-controls="category-panel-3" id="category-3">
                                <i class="category-icon bi bi-collection"></i>
                                <span class="category-label">Furniture</span>
                                <span class="category-badge">New</span>
                            </button>
                            <button class="category-tab" role="tab" aria-selected="false" aria-controls="category-panel-4" id="category-4">
                                <i class="category-icon bi bi-flower1"></i>
                                <span class="category-label">Garden</span>
                            </button>
                            <button class="category-tab" role="tab" aria-selected="false" aria-controls="category-panel-5" id="category-5">
                                <i class="category-icon bi bi-gift"></i>
                                <span class="category-label">Gift Sets</span>
                            </button>
                        </nav>
                        
                        <div class="category-content">
                            <div class="category-panel active" role="tabpanel" id="category-panel-1" aria-labelledby="category-1">
                                <div class="panel-content">
                                    <h4 class="panel-title">Home Decor Collection</h4>
                                    <p class="panel-description">
                                        Discover our curated selection of home decor items that bring warmth and personality to any space.
                                    </p>
                                    <div class="panel-stats">
                                        <div class="stat-item">
                                            <i class="stat-icon bi bi-box-seam"></i>
                                            <span class="stat-text">245 Products</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="stat-icon bi bi-star-fill"></i>
                                            <span class="stat-text">4.8 Rating</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="stat-icon bi bi-truck"></i>
                                            <span class="stat-text">Free Shipping</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="category-panel" role="tabpanel" id="category-panel-2" aria-labelledby="category-2">
                                <div class="panel-content">
                                    <h4 class="panel-title">Lighting Solutions</h4>
                                    <p class="panel-description">
                                        Illuminate your space with our premium lighting collection.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="category-panel" role="tabpanel" id="category-panel-3" aria-labelledby="category-3">
                                <div class="panel-content">
                                    <h4 class="panel-title">Quality Furniture</h4>
                                    <p class="panel-description">
                                        Handpicked furniture pieces for every room in your home.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="category-panel" role="tabpanel" id="category-panel-4" aria-labelledby="category-4">
                                <div class="panel-content">
                                    <h4 class="panel-title">Garden & Outdoor</h4>
                                    <p class="panel-description">
                                        Transform your outdoor space with our garden collection.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="category-panel" role="tabpanel" id="category-panel-5" aria-labelledby="category-5">
                                <div class="panel-content">
                                    <h4 class="panel-title">Curated Gift Sets</h4>
                                    <p class="panel-description">
                                        Perfect gift combinations for every occasion.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Navigation -->
            <div class="component-demo">
                <div class="demo-content">
                    <div class="filter-navigation">
                        <div class="filter-groups">
                            <!-- Price Range Filter -->
                            <div class="filter-group">
                                <h5 class="filter-group-title">
                                    <i class="bi bi-currency-dollar"></i>
                                    Price Range
                                </h5>
                                <div class="filter-options">
                                    <button class="filter-option active" data-filter="price-all">
                                        <span class="filter-label">All Prices</span>
                                        <span class="filter-count">156</span>
                                    </button>
                                    <button class="filter-option" data-filter="price-under-50">
                                        <span class="filter-label">Under $50</span>
                                        <span class="filter-count">45</span>
                                    </button>
                                    <button class="filter-option" data-filter="price-50-100">
                                        <span class="filter-label">$50 - $100</span>
                                        <span class="filter-count">67</span>
                                    </button>
                                    <button class="filter-option" data-filter="price-over-100">
                                        <span class="filter-label">Over $100</span>
                                        <span class="filter-count">44</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Availability Filter -->
                            <div class="filter-group">
                                <h5 class="filter-group-title">
                                    <i class="bi bi-check-circle"></i>
                                    Availability
                                </h5>
                                <div class="filter-options">
                                    <button class="filter-option active" data-filter="availability-all">
                                        <span class="filter-label">All Items</span>
                                        <span class="filter-count">156</span>
                                    </button>
                                    <button class="filter-option" data-filter="availability-stock">
                                        <span class="filter-label">In Stock</span>
                                        <span class="filter-count">142</span>
                                    </button>
                                    <button class="filter-option" data-filter="availability-sale">
                                        <span class="filter-label">On Sale</span>
                                        <span class="filter-count">23</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Brand Filter -->
                            <div class="filter-group">
                                <h5 class="filter-group-title">
                                    <i class="bi bi-tag"></i>
                                    Brand
                                </h5>
                                <div class="filter-options">
                                    <button class="filter-option active" data-filter="brand-all">
                                        <span class="filter-label">All Brands</span>
                                        <span class="filter-count">156</span>
                                    </button>
                                    <button class="filter-option" data-filter="brand-cozywish">
                                        <span class="filter-label">CozyWish</span>
                                        <span class="filter-count">89</span>
                                    </button>
                                    <button class="filter-option" data-filter="brand-premium">
                                        <span class="filter-label">Premium Collection</span>
                                        <span class="filter-count">34</span>
                                    </button>
                                    <button class="filter-option" data-filter="brand-artisan">
                                        <span class="filter-label">Artisan Made</span>
                                        <span class="filter-count">33</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Product List Navigation -->
        <section class="component-section">
            <div class="section-header">
                <h2 class="section-title">Product List Navigation</h2>
                <p class="section-description">
                    Professional pagination and sorting controls for product listings.
                </p>
            </div>

            <!-- Product List Controls -->
            <div class="component-demo">
                <div class="demo-content">
                    <div class="list-controls">
                        <div class="list-info">
                            <div class="results-summary">
                                <i class="bi bi-grid-3x3-gap"></i>
                                <span class="results-text">Showing <strong>1-12</strong> of <strong>156</strong> products</span>
                            </div>
                        </div>
                        
                        <div class="list-actions">
                            <div class="sort-control">
                                <label for="sort-select" class="sort-label">
                                    <i class="bi bi-sort-down"></i>
                                    Sort by:
                                </label>
                                <select id="sort-select" class="sort-select">
                                    <option value="featured">Featured</option>
                                    <option value="price-low">Price: Low to High</option>
                                    <option value="price-high">Price: High to Low</option>
                                    <option value="newest">Newest First</option>
                                    <option value="rating">Highest Rated</option>
                                    <option value="name">Name A-Z</option>
                                </select>
                            </div>
                            
                            <div class="view-control">
                                <button class="view-option active" data-view="grid">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button class="view-option" data-view="list">
                                    <i class="bi bi-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Professional Pagination -->
            <div class="component-demo">
                <div class="demo-content">
                    <div class="pagination-wrapper">
                        <nav class="pagination-nav" aria-label="Product page navigation">
                            <ul class="pagination-list">
                                <li class="pagination-item">
                                    <button class="pagination-link pagination-prev disabled" disabled aria-label="Previous page">
                                        <i class="bi bi-chevron-left"></i>
                                        <span class="pagination-text">Previous</span>
                                    </button>
                                </li>
                                
                                <li class="pagination-item">
                                    <button class="pagination-link active" aria-current="page">1</button>
                                </li>
                                
                                <li class="pagination-item">
                                    <button class="pagination-link">2</button>
                                </li>
                                
                                <li class="pagination-item">
                                    <button class="pagination-link">3</button>
                                </li>
                                
                                <li class="pagination-item">
                                    <span class="pagination-ellipsis">...</span>
                                </li>
                                
                                <li class="pagination-item">
                                    <button class="pagination-link">13</button>
                                </li>
                                
                                <li class="pagination-item">
                                    <button class="pagination-link pagination-next" aria-label="Next page">
                                        <span class="pagination-text">Next</span>
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </li>
                            </ul>
                        </nav>
                        
                        <div class="pagination-info">
                            <div class="page-size-control">
                                <label for="page-size" class="page-size-label">Show:</label>
                                <select id="page-size" class="page-size-select">
                                    <option value="12">12 products</option>
                                    <option value="24">24 products</option>
                                    <option value="48">48 products</option>
                                    <option value="96">96 products</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Breadcrumb Navigation -->
        <section class="component-section">
            <div class="section-header">
                <h2 class="section-title">Breadcrumb Navigation</h2>
                <p class="section-description">
                    Clear navigation paths that help users understand their location within the site hierarchy.
                </p>
            </div>

            <!-- Basic Breadcrumb -->
            <div class="component-demo">
                <div class="demo-content">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="#">Home</a></li>
                            <li class="breadcrumb-item"><a href="#">Category</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Current Page</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </section>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Category tab functionality
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const categoryNav = this.closest('.category-nav');
                const targetPanel = this.getAttribute('aria-controls');
                
                // Remove active class from all tabs and panels
                categoryNav.querySelectorAll('.category-tab').forEach(t => {
                    t.classList.remove('active');
                    t.setAttribute('aria-selected', 'false');
                });
                categoryNav.querySelectorAll('.category-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Add active class to clicked tab and corresponding panel
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');
                if (targetPanel) {
                    document.getElementById(targetPanel).classList.add('active');
                }
            });
        });

        // Filter functionality
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', function() {
                const filterGroup = this.closest('.filter-group');
                filterGroup.querySelectorAll('.filter-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                this.classList.add('active');
            });
        });

        // Clear all filters
        document.querySelector('.filter-clear')?.addEventListener('click', function() {
            document.querySelectorAll('.filter-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelectorAll('.filter-group').forEach(group => {
                group.querySelector('.filter-option').classList.add('active');
            });
        });

        // View toggle functionality
        document.querySelectorAll('.view-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.view-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                this.classList.add('active');
            });
        });

        // Pagination functionality
        document.querySelectorAll('.pagination-link:not(.disabled)').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (this.classList.contains('disabled')) return;
                
                const pagination = this.closest('.pagination-nav');
                pagination.querySelectorAll('.pagination-link').forEach(l => {
                    l.classList.remove('active');
                    l.removeAttribute('aria-current');
                });
                
                if (!this.classList.contains('pagination-prev') && !this.classList.contains('pagination-next')) {
                    this.classList.add('active');
                    this.setAttribute('aria-current', 'page');
                }
            });
        });


    </script>
</body>
</html> 