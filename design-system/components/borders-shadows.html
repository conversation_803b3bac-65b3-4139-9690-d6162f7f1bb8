<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Border Radius & Shadows</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container py-5">
        <!-- Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center mb-4">
                    <h1 class="text-primary mb-3">Border Radius & Shadows</h1>
                    <p class="text-body lead">Professional border radius and shadow utilities for creating depth and modern design elements.</p>
                </div>
            </div>
        </div>
        
        <!-- Border Radius Scale -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">
                    <i class="bi bi-border-radius me-2"></i>
                    Border Radius Scale
                </h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-white">
                        <h5 class="mb-1">Border Radius Reference</h5>
                        <small class="text-muted">Available border radius classes and values</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Border Radius Scale</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Value</th>
                                                <th>Pixels</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>rounded-0</code></td><td>0</td><td>0px</td></tr>
                                            <tr><td><code>rounded-1</code></td><td>0.125rem</td><td>2px</td></tr>
                                            <tr><td><code>rounded-2</code></td><td>0.25rem</td><td>4px</td></tr>
                                            <tr><td><code>rounded-3</code></td><td>0.375rem</td><td>6px</td></tr>
                                            <tr><td><code>rounded-4</code></td><td>0.5rem</td><td>8px</td></tr>
                                            <tr><td><code>rounded-5</code></td><td>0.75rem</td><td>12px</td></tr>
                                            <tr><td><code>rounded</code></td><td>0.375rem</td><td>6px</td></tr>
                                            <tr><td><code>rounded-pill</code></td><td>50rem</td><td>800px</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Individual Corners</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>rounded-top</code></td><td>Top corners only</td></tr>
                                            <tr><td><code>rounded-end</code></td><td>Right corners only</td></tr>
                                            <tr><td><code>rounded-bottom</code></td><td>Bottom corners only</td></tr>
                                            <tr><td><code>rounded-start</code></td><td>Left corners only</td></tr>
                                            <tr><td><code>rounded-circle</code></td><td>Perfect circle (50%)</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Border Radius Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">
                    <i class="bi bi-border-radius me-2"></i>
                    Border Radius Examples
                </h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-white">
                        <h5 class="mb-1">Visual Border Radius Demonstration</h5>
                        <small class="text-muted">See how different border radius values look</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Border Radius Scale</h6>
                                <div class="bg-primary text-white p-3 rounded-0 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-0 (0px)</span>
                                        <small class="opacity-75">Sharp corners</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-1 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-1 (2px)</span>
                                        <small class="opacity-75">Subtle rounding</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-2 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-2 (4px)</span>
                                        <small class="opacity-75">Soft corners</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-3 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-3 (6px)</span>
                                        <small class="opacity-75">Default style</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-4 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-4 (8px)</span>
                                        <small class="opacity-75">Modern look</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-5 mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-5 (12px)</span>
                                        <small class="opacity-75">Bold rounding</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded (default 6px)</span>
                                        <small class="opacity-75">Standard choice</small>
                                    </div>
                                </div>
                                <div class="bg-primary text-white p-3 rounded-pill shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-pill (pill shape)</span>
                                        <small class="opacity-75">Button style</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Individual Corners & Special Shapes</h6>
                                <div class="bg-secondary text-white p-3 rounded-top mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-top</span>
                                        <small class="opacity-75">Top corners only</small>
                                    </div>
                                </div>
                                <div class="bg-secondary text-white p-3 rounded-end mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-end</span>
                                        <small class="opacity-75">Right corners only</small>
                                    </div>
                                </div>
                                <div class="bg-secondary text-white p-3 rounded-bottom mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-bottom</span>
                                        <small class="opacity-75">Bottom corners only</small>
                                    </div>
                                </div>
                                <div class="bg-secondary text-white p-3 rounded-start mb-3 shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>rounded-start</span>
                                        <small class="opacity-75">Left corners only</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-secondary text-white rounded-circle shadow-sm me-3" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                        <span class="text-center">Avatar</span>
                                    </div>
                                    <div>
                                        <div class="text-primary fw-bold">rounded-circle</div>
                                        <small class="text-muted">Perfect circle (50%)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Shadow Scale -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">
                    <i class="bi bi-box-shadow me-2"></i>
                    Shadow Scale
                </h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-white">
                        <h5 class="mb-1">Shadow Utilities Reference</h5>
                        <small class="text-muted">Available shadow classes and their effects</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Bootstrap Shadow Classes</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>shadow-none</code></td><td>No shadow</td></tr>
                                            <tr><td><code>shadow-sm</code></td><td>Small shadow</td></tr>
                                            <tr><td><code>shadow</code></td><td>Default shadow</td></tr>
                                            <tr><td><code>shadow-lg</code></td><td>Large shadow</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Custom Brand Shadows</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>shadow-brand</code></td><td>Brand-colored shadow</td></tr>
                                            <tr><td><code>shadow-brand-lg</code></td><td>Large brand shadow</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Shadow Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">
                    <i class="bi bi-box-shadow me-2"></i>
                    Shadow Examples
                </h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-white">
                        <h5 class="mb-1">Visual Shadow Demonstration</h5>
                        <small class="text-muted">See how different shadow values look</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Bootstrap Shadow Scale</h6>
                                <div class="bg-white p-4 mb-4 shadow-none border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-none</span>
                                        <small class="text-muted">No shadow</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Clean, flat design with no depth</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-sm border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-sm</span>
                                        <small class="text-muted">Subtle depth</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Light shadow for subtle elevation</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow (default)</span>
                                        <small class="text-muted">Standard depth</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Standard shadow for cards and containers</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-lg border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-lg</span>
                                        <small class="text-muted">Prominent depth</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Large shadow for emphasis and focus</p>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Custom Brand Shadows</h6>
                                <div class="bg-white p-4 mb-4 shadow-brand border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-brand</span>
                                        <small class="text-muted">Brand colored</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Brand-colored shadow for primary elements</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-brand-lg border rounded">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-brand-lg</span>
                                        <small class="text-muted">Large brand shadow</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Large brand shadow for hero elements</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-brand border rounded-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-brand + rounded-3</span>
                                        <small class="text-muted">Combined styling</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Brand shadow with rounded corners</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-brand-lg border rounded-4">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">shadow-brand-lg + rounded-4</span>
                                        <small class="text-muted">Premium styling</small>
                                    </div>
                                    <p class="text-body mb-0 mt-2">Large brand shadow with modern corners</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Combined Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">
                    <i class="bi bi-layers me-2"></i>
                    Combined Examples
                </h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-white">
                        <h5 class="mb-1">Border Radius & Shadow Combinations</h5>
                        <small class="text-muted">Professional combinations for different use cases</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Cards & Containers</h6>
                                <div class="bg-white p-4 mb-4 shadow rounded border">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="bg-primary rounded-circle me-3" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <div class="text-primary fw-bold">Basic Card</div>
                                            <small class="text-muted">shadow + rounded</small>
                                        </div>
                                    </div>
                                    <p class="text-body mb-0">Standard card styling for content containers</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-lg rounded-3 border">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="bg-secondary rounded-circle me-3" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <div class="text-primary fw-bold">Elevated Card</div>
                                            <small class="text-muted">shadow-lg + rounded-3</small>
                                        </div>
                                    </div>
                                    <p class="text-body mb-0">Prominent card for important content</p>
                                </div>
                                <div class="bg-white p-4 mb-4 shadow-brand rounded-4 border">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="bg-accent-light rounded-circle me-3 border" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <div class="text-primary fw-bold">Brand Card</div>
                                            <small class="text-muted">shadow-brand + rounded-4</small>
                                        </div>
                                    </div>
                                    <p class="text-body mb-0">Brand-focused card for primary content</p>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Interactive Elements</h6>
                                <button class="btn btn-primary shadow rounded mb-3 w-100">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>Button with Shadow</span>
                                        <small class="opacity-75">shadow + rounded</small>
                                    </div>
                                </button>
                                <button class="btn btn-secondary shadow-lg rounded-3 mb-3 w-100">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>Elevated Button</span>
                                        <small class="opacity-75">shadow-lg + rounded-3</small>
                                    </div>
                                </button>
                                <button class="btn btn-light shadow-brand rounded-pill mb-3 w-100">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>Pill Button</span>
                                        <small class="opacity-75">shadow-brand + rounded-pill</small>
                                    </div>
                                </button>
                                <div class="bg-light p-3 rounded shadow-sm">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">Interactive Card</span>
                                        <small class="text-muted">shadow-sm + rounded</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Media & Avatars</h6>
                                <div class="bg-light p-4 mb-3 shadow rounded border" style="height: 100px; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-center">
                                        <div class="text-primary fw-bold">Image Container</div>
                                        <small class="text-muted">shadow + rounded</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-primary text-white shadow-lg rounded-circle me-3" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                        <span class="fw-bold">JD</span>
                                    </div>
                                    <div>
                                        <div class="text-primary fw-bold">User Avatar</div>
                                        <small class="text-muted">shadow-lg + rounded-circle</small>
                                    </div>
                                </div>
                                <div class="bg-light p-3 shadow-brand rounded-top border" style="height: 80px; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-center">
                                        <div class="text-primary fw-bold">Media Card</div>
                                        <small class="text-muted">shadow-brand + rounded-top</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 