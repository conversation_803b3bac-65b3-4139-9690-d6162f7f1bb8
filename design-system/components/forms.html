<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Components - CozyWish Design System</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-primary mb-4">Form Components</h1>
                <p class="lead mb-5">Comprehensive form components for CozyWish applications, including input fields, select components, and validation states.</p>
            </div>
        </div>

        <!-- Input Fields Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Input Fields</h2>
            
            <!-- Text Inputs -->
            <div class="form-example">
                <h5>Text Inputs</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="basicText" class="form-label">Basic Text Input</label>
                        <input type="text" class="form-control" id="basicText" placeholder="Enter your name">
                    </div>
                    <div class="col-md-6">
                        <label for="requiredText" class="form-label">Required Text Input <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="requiredText" placeholder="Required field" required>
                    </div>
                    <div class="col-md-6">
                        <label for="disabledText" class="form-label">Disabled Text Input</label>
                        <input type="text" class="form-control" id="disabledText" value="Disabled field" disabled>
                    </div>
                    <div class="col-md-6">
                        <label for="readonlyText" class="form-label">Read-only Text Input</label>
                        <input type="text" class="form-control" id="readonlyText" value="Read-only field" readonly>
                    </div>
                </div>
            </div>

            <!-- Email Inputs -->
            <div class="form-example">
                <h5>Email Inputs</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="emailInput" class="form-label">Email Input</label>
                        <input type="email" class="form-control" id="emailInput" placeholder="Enter your email">
                        <div class="form-text">We'll never share your email with anyone else.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="emailInputValid" class="form-label">Valid Email Input</label>
                        <input type="email" class="form-control is-valid" id="emailInputValid" value="<EMAIL>">
                        <div class="valid-feedback">Looks good!</div>
                    </div>
                </div>
            </div>

            <!-- Password Inputs with Toggle -->
            <div class="form-example">
                <h5>Password Inputs (with Toggle)</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="passwordInput" class="form-label">Password Input</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="passwordInput" placeholder="Enter password">
                            <button class="btn btn-outline-secondary password-toggle" type="button" onclick="togglePassword('passwordInput')">
                                <i class="bi bi-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Confirm password">
                            <button class="btn btn-outline-secondary password-toggle" type="button" onclick="togglePassword('confirmPassword')">
                                <i class="bi bi-eye" id="confirmPasswordToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Number Inputs -->
            <div class="form-example">
                <h5>Number Inputs</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="basicNumber" class="form-label">Basic Number Input</label>
                        <input type="number" class="form-control" id="basicNumber" placeholder="Enter a number">
                    </div>
                    <div class="col-md-4">
                        <label for="numberWithMinMax" class="form-label">Number with Min/Max</label>
                        <input type="number" class="form-control" id="numberWithMinMax" min="0" max="100" placeholder="0-100">
                    </div>
                    <div class="col-md-4">
                        <label for="numberWithStep" class="form-label">Number with Step</label>
                        <input type="number" class="form-control" id="numberWithStep" step="0.5" placeholder="Step by 0.5">
                    </div>
                </div>
            </div>

            <!-- Date and Time Inputs -->
            <div class="form-example">
                <h5>Date and Time Inputs</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="dateInput" class="form-label">Date Input</label>
                        <input type="date" class="form-control" id="dateInput">
                    </div>
                    <div class="col-md-4">
                        <label for="timeInput" class="form-label">Time Input</label>
                        <input type="time" class="form-control" id="timeInput">
                    </div>
                    <div class="col-md-4">
                        <label for="datetimeInput" class="form-label">Date & Time Input</label>
                        <input type="datetime-local" class="form-control" id="datetimeInput">
                    </div>
                </div>
            </div>

            <!-- Search Inputs -->
            <div class="form-example">
                <h5>Search Inputs (with Icon)</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="searchInput" class="form-label">Search Input</label>
                        <div class="search-input">
                            <i class="bi bi-search search-icon"></i>
                            <input type="search" class="form-control" id="searchInput" placeholder="Search...">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="searchWithButton" class="form-label">Search with Button</label>
                        <div class="input-group">
                            <input type="search" class="form-control" id="searchWithButton" placeholder="Search...">
                            <button class="btn btn-primary" type="button">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Labels -->
            <div class="form-example">
                <h5>Floating Labels</h5>
                <div class="row g-3">
                    <div class="col-md-6 mb-3">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="floatingText" placeholder=" ">
                            <label for="floatingText">Full Name</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-floating">
                            <input type="email" class="form-control" id="floatingEmail" placeholder=" ">
                            <label for="floatingEmail">Email address</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-floating">
                            <textarea class="form-control" id="floatingTextarea" placeholder=" " style="height: 100px"></textarea>
                            <label for="floatingTextarea">Comments</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-floating">
                            <select class="form-select" id="floatingSelect">
                                <option value="">Choose...</option>
                                <option value="1">Option 1</option>
                                <option value="2">Option 2</option>
                                <option value="3">Option 3</option>
                            </select>
                            <label for="floatingSelect">Works with selects</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Textarea -->
            <div class="form-example">
                <h5>Textarea</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="basicTextarea" class="form-label">Basic Textarea</label>
                        <textarea class="form-control" id="basicTextarea" rows="3" placeholder="Enter your message here..."></textarea>
                    </div>
                    <div class="col-md-6">
                        <label for="resizableTextarea" class="form-label">Resizable Textarea</label>
                        <textarea class="form-control" id="resizableTextarea" rows="3" style="resize: both;" placeholder="This textarea is resizable..."></textarea>
                    </div>
                    <div class="col-md-6">
                        <label for="autoResizeTextarea" class="form-label">Auto-resizing Textarea</label>
                        <textarea class="form-control auto-resize" id="autoResizeTextarea" rows="3" placeholder="This textarea automatically resizes as you type..."></textarea>
                    </div>
                    <div class="col-md-6">
                        <label for="charCounterTextarea" class="form-label">Textarea with Character Counter</label>
                        <textarea class="form-control" id="charCounterTextarea" rows="3" maxlength="200" placeholder="Type your message here (max 200 characters)..." oninput="updateCharCounter(this)"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span>/200 characters
                        </div>
                    </div>
                </div>
            </div>

            <!-- Range Inputs -->
            <div class="form-example">
                <h5>Range Inputs</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="basicRange" class="form-label">Basic Range: <span id="rangeValue">50</span></label>
                        <input type="range" class="form-range" id="basicRange" min="0" max="100" value="50" oninput="updateRangeValue(this.value)">
                    </div>
                    <div class="col-md-6">
                        <label for="customRange" class="form-label">Custom Range: <span id="customRangeValue">25</span></label>
                        <input type="range" class="form-range" id="customRange" min="0" max="100" step="5" value="25" oninput="updateCustomRangeValue(this.value)">
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkbox & Radio Components Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Checkbox & Radio Components</h2>
            
            <!-- Individual Checkboxes -->
            <div class="form-example">
                <h5>Individual Checkboxes</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="basicCheckbox">
                            <label class="form-check-label" for="basicCheckbox">
                                Basic checkbox
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="checkedCheckbox" checked>
                            <label class="form-check-label" for="checkedCheckbox">
                                Checked checkbox
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="disabledCheckbox" disabled>
                            <label class="form-check-label" for="disabledCheckbox">
                                Disabled checkbox
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="disabledCheckedCheckbox" checked disabled>
                            <label class="form-check-label" for="disabledCheckedCheckbox">
                                Disabled checked checkbox
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="requiredCheckbox" required>
                            <label class="form-check-label" for="requiredCheckbox">
                                Required checkbox <span class="text-danger">*</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="validCheckbox" class="is-valid">
                            <label class="form-check-label" for="validCheckbox">
                                Valid checkbox
                            </label>
                            <div class="valid-feedback">Looks good!</div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="invalidCheckbox" class="is-invalid">
                            <label class="form-check-label" for="invalidCheckbox">
                                Invalid checkbox
                            </label>
                            <div class="invalid-feedback">Please check this box.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Checkbox Groups -->
            <div class="form-example">
                <h5>Checkbox Groups</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Interests</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="interest1" name="interests">
                            <label class="form-check-label" for="interest1">
                                Technology
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="interest2" name="interests">
                            <label class="form-check-label" for="interest2">
                                Travel
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="interest3" name="interests">
                            <label class="form-check-label" for="interest3">
                                Cooking
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="interest4" name="interests">
                            <label class="form-check-label" for="interest4">
                                Sports
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Notification Preferences</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotif" name="notifications">
                            <label class="form-check-label" for="emailNotif">
                                Email notifications
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="smsNotif" name="notifications">
                            <label class="form-check-label" for="smsNotif">
                                SMS notifications
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="pushNotif" name="notifications">
                            <label class="form-check-label" for="pushNotif">
                                Push notifications
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Radio Buttons -->
            <div class="form-example">
                <h5>Radio Buttons</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="basicRadio" id="radio1">
                            <label class="form-check-label" for="radio1">
                                Option 1
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="basicRadio" id="radio2" checked>
                            <label class="form-check-label" for="radio2">
                                Option 2 (checked)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="basicRadio" id="radio3">
                            <label class="form-check-label" for="radio3">
                                Option 3
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="basicRadio" id="radio4" disabled>
                            <label class="form-check-label" for="radio4">
                                Option 4 (disabled)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="validRadio" id="validRadio1" class="is-valid">
                            <label class="form-check-label" for="validRadio1">
                                Valid option 1
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="validRadio" id="validRadio2" class="is-valid">
                            <label class="form-check-label" for="validRadio2">
                                Valid option 2
                            </label>
                        </div>
                        <div class="valid-feedback">Please select an option.</div>
                    </div>
                </div>
            </div>

            <!-- Radio Groups -->
            <div class="form-example">
                <h5>Radio Groups</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Gender</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="gender" id="male">
                            <label class="form-check-label" for="male">
                                Male
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="gender" id="female">
                            <label class="form-check-label" for="female">
                                Female
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="gender" id="other">
                            <label class="form-check-label" for="other">
                                Other
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="gender" id="preferNot">
                            <label class="form-check-label" for="preferNot">
                                Prefer not to say
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Experience Level</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="experience" id="beginner">
                            <label class="form-check-label" for="beginner">
                                Beginner
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="experience" id="intermediate">
                            <label class="form-check-label" for="intermediate">
                                Intermediate
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="experience" id="advanced">
                            <label class="form-check-label" for="advanced">
                                Advanced
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="experience" id="expert">
                            <label class="form-check-label" for="expert">
                                Expert
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Styled Checkboxes and Radios -->
            <div class="form-example">
                <h5>Custom Styled Checkboxes and Radios</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Custom Checkboxes</label>
                        <div class="form-check custom-checkbox">
                            <input class="form-check-input" type="checkbox" id="customCheck1">
                            <label class="form-check-label" for="customCheck1">
                                <i class="bi bi-check-circle-fill"></i> Custom styled checkbox
                            </label>
                        </div>
                        <div class="form-check custom-checkbox">
                            <input class="form-check-input" type="checkbox" id="customCheck2" checked>
                            <label class="form-check-label" for="customCheck2">
                                <i class="bi bi-check-circle-fill"></i> Checked custom checkbox
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Custom Radio Buttons</label>
                        <div class="form-check custom-radio">
                            <input class="form-check-input" type="radio" name="customRadio" id="customRadio1">
                            <label class="form-check-label" for="customRadio1">
                                <i class="bi bi-circle-fill"></i> Custom styled radio
                            </label>
                        </div>
                        <div class="form-check custom-radio">
                            <input class="form-check-input" type="radio" name="customRadio" id="customRadio2" checked>
                            <label class="form-check-label" for="customRadio2">
                                <i class="bi bi-circle-fill"></i> Selected custom radio
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Upload Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">File Upload Components</h2>
            
            <!-- Single File Upload -->
            <div class="form-example">
                <h5>Single File Upload</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="singleFile" class="form-label">Basic File Upload</label>
                        <input type="file" class="form-control" id="singleFile">
                        <div class="form-text">Choose a file to upload</div>
                    </div>
                    <div class="col-md-6">
                        <label for="imageFile" class="form-label">Image File Upload</label>
                        <input type="file" class="form-control" id="imageFile" accept="image/*">
                        <div class="form-text">Only image files are allowed</div>
                    </div>
                    <div class="col-md-6">
                        <label for="documentFile" class="form-label">Document Upload</label>
                        <input type="file" class="form-control" id="documentFile" accept=".pdf,.doc,.docx,.txt">
                        <div class="form-text">PDF, DOC, DOCX, or TXT files only</div>
                    </div>
                    <div class="col-md-6">
                        <label for="customFile" class="form-label">Custom Styled File Upload</label>
                        <div class="custom-file-upload">
                            <input type="file" class="form-control" id="customFile" hidden>
                            <label for="customFile" class="custom-file-label">
                                <i class="bi bi-cloud-upload"></i>
                                <span>Choose a file or drag it here</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multiple File Upload -->
            <div class="form-example">
                <h5>Multiple File Upload</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="multipleFiles" class="form-label">Multiple Files</label>
                        <input type="file" class="form-control" id="multipleFiles" multiple>
                        <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple files</div>
                    </div>
                    <div class="col-md-6">
                        <label for="multipleImages" class="form-label">Multiple Images</label>
                        <input type="file" class="form-control" id="multipleImages" multiple accept="image/*">
                        <div class="form-text">Select multiple image files</div>
                    </div>
                </div>
            </div>

            <!-- Drag & Drop Interface -->
            <div class="form-example">
                <h5>Drag & Drop Interface</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Drag & Drop Zone</label>
                        <div class="drag-drop-zone" id="dragDropZone" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                            <div class="drag-drop-content">
                                <i class="bi bi-cloud-upload drag-drop-icon"></i>
                                <h6>Drag & Drop files here</h6>
                                <p class="text-muted">or click to browse</p>
                                <input type="file" id="dragDropInput" multiple hidden onchange="handleFileSelect(event)">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('dragDropInput').click()">
                                    Browse Files
                                </button>
                            </div>
                        </div>
                        <div id="dragDropFiles" class="mt-2"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Image Drag & Drop</label>
                        <div class="drag-drop-zone image-drop-zone" id="imageDropZone" ondrop="handleImageDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                            <div class="drag-drop-content">
                                <i class="bi bi-image drag-drop-icon"></i>
                                <h6>Drag & Drop images here</h6>
                                <p class="text-muted">Supports JPG, PNG, GIF</p>
                                <input type="file" id="imageDropInput" multiple accept="image/*" hidden onchange="handleImageSelect(event)">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('imageDropInput').click()">
                                    Select Images
                                </button>
                            </div>
                        </div>
                        <div id="imagePreviewContainer" class="mt-2"></div>
                    </div>
                </div>
            </div>

            <!-- Image Preview -->
            <div class="form-example">
                <h5>Image Preview</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="imagePreviewInput" class="form-label">Single Image with Preview</label>
                        <input type="file" class="form-control" id="imagePreviewInput" accept="image/*" onchange="previewImage(this)">
                        <div id="singleImagePreview" class="mt-2"></div>
                    </div>
                    <div class="col-md-6">
                        <label for="multipleImagePreview" class="form-label">Multiple Images with Preview</label>
                        <input type="file" class="form-control" id="multipleImagePreview" multiple accept="image/*" onchange="previewMultipleImages(this)">
                        <div id="multipleImagePreviewContainer" class="mt-2 d-flex flex-wrap gap-2"></div>
                    </div>
                </div>
            </div>

            <!-- File Upload Progress -->
            <div class="form-example">
                <h5>File Upload Progress</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="progressFile" class="form-label">File with Progress Bar</label>
                        <input type="file" class="form-control" id="progressFile" onchange="simulateUpload(this)">
                        <div class="progress mt-2" id="uploadProgress" style="display: none;">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="uploadStatus" class="mt-2"></div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Multiple Files Progress</label>
                        <input type="file" class="form-control" id="multipleProgressFiles" multiple onchange="simulateMultipleUploads(this)">
                        <div id="multipleProgressContainer" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Select Components Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Select Components</h2>
            
            <!-- Single Select Dropdowns -->
            <div class="form-example">
                <h5>Single Select Dropdowns</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="basicSelect" class="form-label">Basic Select</label>
                        <select class="form-select" id="basicSelect">
                            <option selected>Choose an option...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                            <option value="3">Option 3</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="customSelect" class="form-label">Custom Styled Select</label>
                        <select class="form-select custom-select" id="customSelect">
                            <option selected>Choose an option...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                            <option value="3">Option 3</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="selectWithGroups" class="form-label">Select with Option Groups</label>
                        <select class="form-select" id="selectWithGroups">
                            <option selected>Choose a category...</option>
                            <optgroup label="Fruits">
                                <option value="apple">Apple</option>
                                <option value="banana">Banana</option>
                                <option value="orange">Orange</option>
                            </optgroup>
                            <optgroup label="Vegetables">
                                <option value="carrot">Carrot</option>
                                <option value="broccoli">Broccoli</option>
                                <option value="spinach">Spinach</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="selectWithIcons" class="form-label">Select with Icons</label>
                        <select class="form-select" id="selectWithIcons">
                            <option selected>Choose a payment method...</option>
                            <option value="credit">💳 Credit Card</option>
                            <option value="debit">💳 Debit Card</option>
                            <option value="paypal">📧 PayPal</option>
                            <option value="bank">🏦 Bank Transfer</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Multi-select Dropdowns -->
            <div class="form-example">
                <h5>Multi-select Dropdowns</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="multiSelect" class="form-label">Multi-select (Native)</label>
                        <select class="form-select" id="multiSelect" multiple size="4">
                            <option value="option1">Option 1</option>
                            <option value="option2">Option 2</option>
                            <option value="option3">Option 3</option>
                            <option value="option4">Option 4</option>
                            <option value="option5">Option 5</option>
                        </select>
                        <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple options.</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Custom Multi-select</label>
                        <div class="multi-select-container" id="customMultiSelect">
                            <div class="d-flex flex-wrap align-items-center">
                                <span class="multi-select-tag">
                                    Option 1 <span class="remove-tag" onclick="removeTag(this)">×</span>
                                </span>
                                <span class="multi-select-tag">
                                    Option 3 <span class="remove-tag" onclick="removeTag(this)">×</span>
                                </span>
                                <input type="text" class="multi-select-input" placeholder="Type to add options..." onkeydown="handleMultiSelectInput(event)">
                            </div>
                        </div>
                        <div class="form-text">Type and press Enter to add options, click × to remove.</div>
                    </div>
                </div>
            </div>

            <!-- Searchable Selects -->
            <div class="form-example">
                <h5>Searchable Selects</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="searchableSelect" class="form-label">Searchable Select</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchableSelect" placeholder="Search options..." oninput="filterSelectOptions(this.value)">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu" id="searchableDropdown">
                                <li><a class="dropdown-item" href="#" onclick="selectOption('option1')">Option 1</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectOption('option2')">Option 2</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectOption('option3')">Option 3</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectOption('option4')">Option 4</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectOption('option5')">Option 5</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="countrySelect" class="form-label">Country Select (Searchable)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="countrySelect" placeholder="Search countries..." oninput="filterCountries(this.value)">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-globe"></i>
                            </button>
                            <ul class="dropdown-menu" id="countryDropdown">
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('US')">🇺🇸 United States</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('CA')">🇨🇦 Canada</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('UK')">🇬🇧 United Kingdom</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('AU')">🇦🇺 Australia</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('DE')">🇩🇪 Germany</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('FR')">🇫🇷 France</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('JP')">🇯🇵 Japan</a></li>
                                <li><a class="dropdown-item" href="#" onclick="selectCountry('BR')">🇧🇷 Brazil</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Select Styling -->
            <div class="form-example">
                <h5>Custom Select Styling</h5>
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="largeSelect" class="form-label">Large Select</label>
                        <select class="form-select form-select-lg" id="largeSelect">
                            <option selected>Large select...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                            <option value="3">Option 3</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="smallSelect" class="form-label">Small Select</label>
                        <select class="form-select form-select-sm" id="smallSelect">
                            <option selected>Small select...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                            <option value="3">Option 3</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="disabledSelect" class="form-label">Disabled Select</label>
                        <select class="form-select" id="disabledSelect" disabled>
                            <option>Disabled select...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Form Validation Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Enhanced Form Validation</h2>
            
            <!-- Basic Validation States -->
            <div class="form-example">
                <h5>Basic Validation States</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="validInput" class="form-label">Valid Input</label>
                        <input type="text" class="form-control is-valid" id="validInput" value="Valid input">
                        <div class="valid-feedback">Looks good!</div>
                    </div>
                    <div class="col-md-6">
                        <label for="invalidInput" class="form-label">Invalid Input</label>
                        <input type="text" class="form-control is-invalid" id="invalidInput" value="Invalid input">
                        <div class="invalid-feedback">Please provide a valid input.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="validSelect" class="form-label">Valid Select</label>
                        <select class="form-select is-valid" id="validSelect">
                            <option value="">Choose...</option>
                            <option value="1" selected>Valid option</option>
                            <option value="2">Option 2</option>
                        </select>
                        <div class="valid-feedback">Looks good!</div>
                    </div>
                    <div class="col-md-6">
                        <label for="invalidSelect" class="form-label">Invalid Select</label>
                        <select class="form-select is-invalid" id="invalidSelect">
                            <option value="" selected>Choose...</option>
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                        </select>
                        <div class="invalid-feedback">Please select a valid option.</div>
                    </div>
                </div>
            </div>

            <!-- Real-time Validation -->
            <div class="form-example">
                <h5>Real-time Validation</h5>
                <form id="realTimeValidationForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="emailValidation" class="form-label">Email Validation</label>
                            <input type="email" class="form-control" id="emailValidation" placeholder="Enter email" onblur="validateEmail(this)">
                            <div class="invalid-feedback" id="emailFeedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="passwordValidation" class="form-label">Password Validation</label>
                            <input type="password" class="form-control" id="passwordValidation" placeholder="Enter password" oninput="validatePassword(this)">
                            <div class="form-text">
                                <small id="passwordStrength"></small>
                            </div>
                            <div class="invalid-feedback" id="passwordFeedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="phoneValidation" class="form-label">Phone Validation</label>
                            <input type="tel" class="form-control" id="phoneValidation" placeholder="(*************" onblur="validatePhone(this)">
                            <div class="invalid-feedback" id="phoneFeedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label for="urlValidation" class="form-label">URL Validation</label>
                            <input type="url" class="form-control" id="urlValidation" placeholder="https://example.com" onblur="validateURL(this)">
                            <div class="invalid-feedback" id="urlFeedback"></div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Help Text and Validation Messages -->
            <div class="form-example">
                <h5>Help Text and Validation Messages</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="usernameHelp" class="form-label">Username</label>
                        <input type="text" class="form-control" id="usernameHelp" placeholder="Enter username">
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i> Username must be 3-20 characters long and contain only letters, numbers, and underscores.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="passwordHelp" class="form-label">Password</label>
                        <input type="password" class="form-control" id="passwordHelp" placeholder="Enter password">
                        <div class="form-text">
                            <i class="bi bi-shield-check"></i> Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="confirmPasswordHelp" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPasswordHelp" placeholder="Confirm password">
                        <div class="form-text">
                            <i class="bi bi-check-circle"></i> Passwords must match.
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="ageHelp" class="form-label">Age</label>
                        <input type="number" class="form-control" id="ageHelp" placeholder="Enter your age" min="13" max="120">
                        <div class="form-text">
                            <i class="bi bi-calendar"></i> You must be at least 13 years old to register.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Validation Messages -->
            <div class="form-example">
                <h5>Custom Validation Messages</h5>
                <form id="customValidationForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="customRequired" class="form-label">Required Field <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customRequired" required>
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-triangle"></i> This field is required and cannot be left empty.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customMinLength" class="form-label">Minimum Length</label>
                            <input type="text" class="form-control" id="customMinLength" minlength="5">
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-triangle"></i> This field must be at least 5 characters long.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customPattern" class="form-label">Pattern Validation</label>
                            <input type="text" class="form-control" id="customPattern" pattern="[A-Za-z]{3}" placeholder="3 letters only">
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-triangle"></i> Please enter exactly 3 letters (A-Z, a-z).
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="customRange" class="form-label">Range Validation</label>
                            <input type="number" class="form-control" id="customRange" min="1" max="100" placeholder="1-100">
                            <div class="invalid-feedback">
                                <i class="bi bi-exclamation-triangle"></i> Please enter a number between 1 and 100.
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Validation Summary -->
            <div class="form-example">
                <h5>Validation Summary</h5>
                <form id="validationSummaryForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="summaryName" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="summaryName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="summaryEmail" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="summaryEmail" required>
                        </div>
                        <div class="col-12">
                            <label for="summaryMessage" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="summaryMessage" rows="3" required></textarea>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary" onclick="validateForm(event)">Submit Form</button>
                        </div>
                    </div>
                </form>
                <div id="validationSummary" class="mt-3" style="display: none;">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-exclamation-triangle"></i> Please correct the following errors:</h6>
                        <ul id="validationErrors" class="mb-0"></ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Form Layouts Section -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Advanced Form Layouts</h2>
            
            <!-- Horizontal Forms -->
            <div class="form-example">
                <h5>Horizontal Forms</h5>
                <form>
                    <div class="row mb-3">
                        <label for="horizontalEmail" class="col-sm-3 col-form-label">Email</label>
                        <div class="col-sm-9">
                            <input type="email" class="form-control" id="horizontalEmail" placeholder="Enter your email">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="horizontalPassword" class="col-sm-3 col-form-label">Password</label>
                        <div class="col-sm-9">
                            <input type="password" class="form-control" id="horizontalPassword" placeholder="Enter your password">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="horizontalSelect" class="col-sm-3 col-form-label">Country</label>
                        <div class="col-sm-9">
                            <select class="form-select" id="horizontalSelect">
                                <option selected>Choose...</option>
                                <option value="1">United States</option>
                                <option value="2">Canada</option>
                                <option value="3">United Kingdom</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-9 offset-sm-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="horizontalCheck">
                                <label class="form-check-label" for="horizontalCheck">
                                    Remember me
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-9 offset-sm-3">
                            <button type="submit" class="btn btn-primary">Sign in</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Inline Forms -->
            <div class="form-example">
                <h5>Inline Forms</h5>
                <form class="row g-3 align-items-end">
                    <div class="col-auto">
                        <label for="inlineEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="inlineEmail" placeholder="Enter email">
                    </div>
                    <div class="col-auto">
                        <label for="inlinePassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="inlinePassword" placeholder="Password">
                    </div>
                    <div class="col-auto">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheck">
                            <label class="form-check-label" for="inlineCheck">
                                Remember me
                            </label>
                        </div>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">Sign in</button>
                    </div>
                </form>
            </div>

            <!-- Form Groups -->
            <div class="form-example">
                <h5>Form Groups</h5>
                <form>
                    <div class="form-group mb-3">
                        <label for="groupName" class="form-label">Full Name</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="groupName" placeholder="First name">
                            <input type="text" class="form-control" placeholder="Last name">
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="groupEmail" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="email" class="form-control" id="groupEmail" placeholder="username">
                            <span class="input-group-text">.com</span>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="groupAmount" class="form-label">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="groupAmount" placeholder="0.00">
                            <span class="input-group-text">.00</span>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="groupWebsite" class="form-label">Website</label>
                        <div class="input-group">
                            <span class="input-group-text">https://</span>
                            <input type="text" class="form-control" id="groupWebsite" placeholder="example.com">
                        </div>
                    </div>
                </form>
            </div>

            <!-- Responsive Form Grid -->
            <div class="form-example">
                <h5>Responsive Form Grid</h5>
                <form>
                    <div class="row g-3">
                        <div class="col-12 col-md-6 col-lg-4">
                            <label for="gridName" class="form-label">Name</label>
                            <input type="text" class="form-control" id="gridName" placeholder="Full name">
                        </div>
                        <div class="col-12 col-md-6 col-lg-4">
                            <label for="gridEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="gridEmail" placeholder="Email address">
                        </div>
                        <div class="col-12 col-md-6 col-lg-4">
                            <label for="gridPhone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="gridPhone" placeholder="Phone number">
                        </div>
                        <div class="col-12 col-md-6">
                            <label for="gridAddress" class="form-label">Address</label>
                            <input type="text" class="form-control" id="gridAddress" placeholder="Street address">
                        </div>
                        <div class="col-12 col-md-6">
                            <label for="gridCity" class="form-label">City</label>
                            <input type="text" class="form-control" id="gridCity" placeholder="City">
                        </div>
                        <div class="col-12">
                            <label for="gridMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="gridMessage" rows="3" placeholder="Your message"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Form Layout Examples -->
        <div class="form-section">
            <h2 class="text-primary mb-4">Form Layout Examples</h2>
            
            <div class="form-example">
                <h5>Contact Form</h5>
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="firstName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="lastName" required>
                        </div>
                        <div class="col-12">
                            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone">
                        </div>
                        <div class="col-md-6">
                            <label for="subject" class="form-label">Subject</label>
                            <select class="form-select" id="subject">
                                <option selected>Choose a subject...</option>
                                <option value="general">General Inquiry</option>
                                <option value="support">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" rows="4" required placeholder="Enter your message here..."></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Send Message</button>
                            <button type="reset" class="btn btn-outline-secondary ms-2">Reset Form</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <script>
        // Password toggle functionality
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId === 'passwordInput' ? 'passwordToggleIcon' : 'confirmPasswordToggleIcon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }

        // Range value updates
        function updateRangeValue(value) {
            document.getElementById('rangeValue').textContent = value;
        }

        function updateCustomRangeValue(value) {
            document.getElementById('customRangeValue').textContent = value;
        }

        // Multi-select functionality
        function handleMultiSelectInput(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const value = input.value.trim();
                
                if (value) {
                    addTag(value);
                    input.value = '';
                }
            }
        }

        function addTag(text) {
            const container = document.getElementById('customMultiSelect');
            const tag = document.createElement('span');
            tag.className = 'multi-select-tag';
            tag.innerHTML = `${text} <span class="remove-tag" onclick="removeTag(this)">×</span>`;
            
            const inputContainer = container.querySelector('.d-flex');
            inputContainer.insertBefore(tag, inputContainer.lastElementChild);
        }

        function removeTag(element) {
            element.parentElement.remove();
        }

        // Searchable select functionality
        function filterSelectOptions(searchTerm) {
            const dropdown = document.getElementById('searchableDropdown');
            const items = dropdown.querySelectorAll('.dropdown-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function selectOption(value) {
            document.getElementById('searchableSelect').value = value;
        }

        function filterCountries(searchTerm) {
            const dropdown = document.getElementById('countryDropdown');
            const items = dropdown.querySelectorAll('.dropdown-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function selectCountry(code) {
            const input = document.getElementById('countrySelect');
            const selectedItem = document.querySelector(`[onclick="selectCountry('${code}')"]`);
            input.value = selectedItem.textContent;
        }

        // Initialize range values
        document.addEventListener('DOMContentLoaded', function() {
            updateRangeValue(50);
            updateCustomRangeValue(25);
        });

        // Auto-resize textarea functionality
        document.addEventListener('DOMContentLoaded', function() {
            const autoResizeTextareas = document.querySelectorAll('.auto-resize');
            autoResizeTextareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });

        // Character counter functionality
        function updateCharCounter(textarea) {
            const charCount = document.getElementById('charCount');
            const currentLength = textarea.value.length;
            const maxLength = textarea.maxLength;
            charCount.textContent = currentLength;
            
            if (currentLength > maxLength * 0.9) {
                charCount.style.color = '#dc3545';
            } else if (currentLength > maxLength * 0.7) {
                charCount.style.color = '#ffc107';
            } else {
                charCount.style.color = '#6c757d';
            }
        }

        // File upload functionality
        function handleDrop(event) {
            event.preventDefault();
            const files = event.dataTransfer.files;
            displayFiles(files, 'dragDropFiles');
        }

        function handleImageDrop(event) {
            event.preventDefault();
            const files = event.dataTransfer.files;
            displayImageFiles(files, 'imagePreviewContainer');
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            displayFiles(files, 'dragDropFiles');
        }

        function handleImageSelect(event) {
            const files = event.target.files;
            displayImageFiles(files, 'imagePreviewContainer');
        }

        function displayFiles(files, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            Array.from(files).forEach(file => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'alert alert-info d-flex align-items-center';
                fileDiv.innerHTML = `
                    <i class="bi bi-file-earmark me-2"></i>
                    <div>
                        <strong>${file.name}</strong> (${formatFileSize(file.size)})
                    </div>
                `;
                container.appendChild(fileDiv);
            });
        }

        function displayImageFiles(files, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imgDiv = document.createElement('div');
                        imgDiv.className = 'd-inline-block me-2 mb-2';
                        imgDiv.innerHTML = `
                            <img src="${e.target.result}" alt="${file.name}" style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                            <div class="small text-muted">${file.name}</div>
                        `;
                        container.appendChild(imgDiv);
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Image preview functionality
        function previewImage(input) {
            const preview = document.getElementById('singleImagePreview');
            preview.innerHTML = '';
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                    `;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function previewMultipleImages(input) {
            const container = document.getElementById('multipleImagePreviewContainer');
            container.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const imgDiv = document.createElement('div');
                            imgDiv.className = 'd-inline-block me-2 mb-2';
                            imgDiv.innerHTML = `
                                <img src="${e.target.result}" alt="${file.name}" style="width: 80px; height: 80px; object-fit: cover; border-radius: 6px;">
                            `;
                            container.appendChild(imgDiv);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        }

        // Upload progress simulation
        function simulateUpload(input) {
            const progress = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const status = document.getElementById('uploadStatus');
            
            if (input.files && input.files[0]) {
                progress.style.display = 'block';
                status.innerHTML = '<small class="text-info">Uploading...</small>';
                
                let width = 0;
                const interval = setInterval(() => {
                    width += Math.random() * 15;
                    if (width >= 100) {
                        width = 100;
                        clearInterval(interval);
                        status.innerHTML = '<small class="text-success">Upload complete!</small>';
                    }
                    progressBar.style.width = width + '%';
                    progressBar.textContent = Math.round(width) + '%';
                }, 200);
            }
        }

        function simulateMultipleUploads(input) {
            const container = document.getElementById('multipleProgressContainer');
            container.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach((file, index) => {
                    const fileDiv = document.createElement('div');
                    fileDiv.className = 'mb-2';
                    fileDiv.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>${file.name}</small>
                            <small class="text-info">Uploading...</small>
                        </div>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" id="progress-${index}" role="progressbar" style="width: 0%"></div>
                        </div>
                    `;
                    container.appendChild(fileDiv);
                    
                    // Simulate upload progress
                    let width = 0;
                    const interval = setInterval(() => {
                        width += Math.random() * 20;
                        if (width >= 100) {
                            width = 100;
                            clearInterval(interval);
                            fileDiv.querySelector('.text-info').textContent = 'Complete';
                            fileDiv.querySelector('.text-info').className = 'text-success';
                        }
                        fileDiv.querySelector('.progress-bar').style.width = width + '%';
                    }, 300);
                });
            }
        }

        // Validation functions
        function validateEmail(input) {
            const email = input.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const feedback = document.getElementById('emailFeedback');
            
            if (email && !emailRegex.test(email)) {
                input.classList.add('is-invalid');
                feedback.textContent = 'Please enter a valid email address.';
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                feedback.textContent = '';
            }
        }

        function validatePassword(input) {
            const password = input.value;
            const strength = document.getElementById('passwordStrength');
            const feedback = document.getElementById('passwordFeedback');
            
            let score = 0;
            let feedbackText = '';
            
            if (password.length >= 8) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;
            
            switch(score) {
                case 0:
                case 1:
                    strength.textContent = 'Very Weak';
                    strength.className = 'text-danger';
                    feedbackText = 'Password is too weak.';
                    break;
                case 2:
                    strength.textContent = 'Weak';
                    strength.className = 'text-warning';
                    feedbackText = 'Password is weak.';
                    break;
                case 3:
                    strength.textContent = 'Fair';
                    strength.className = 'text-info';
                    feedbackText = 'Password is fair.';
                    break;
                case 4:
                    strength.textContent = 'Good';
                    strength.className = 'text-primary';
                    feedbackText = 'Password is good.';
                    break;
                case 5:
                    strength.textContent = 'Strong';
                    strength.className = 'text-success';
                    feedbackText = 'Password is strong!';
                    break;
            }
            
            if (password.length < 8) {
                input.classList.add('is-invalid');
                feedback.textContent = feedbackText;
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                feedback.textContent = '';
            }
        }

        function validatePhone(input) {
            const phone = input.value.replace(/\D/g, '');
            const feedback = document.getElementById('phoneFeedback');
            
            if (phone && phone.length !== 10) {
                input.classList.add('is-invalid');
                feedback.textContent = 'Please enter a valid 10-digit phone number.';
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                feedback.textContent = '';
            }
        }

        function validateURL(input) {
            const url = input.value;
            const urlRegex = /^https?:\/\/.+/;
            const feedback = document.getElementById('urlFeedback');
            
            if (url && !urlRegex.test(url)) {
                input.classList.add('is-invalid');
                feedback.textContent = 'Please enter a valid URL starting with http:// or https://';
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                feedback.textContent = '';
            }
        }

        function validateForm(event) {
            event.preventDefault();
            const form = document.getElementById('validationSummaryForm');
            const summary = document.getElementById('validationSummary');
            const errorsList = document.getElementById('validationErrors');
            
            const errors = [];
            
            // Check required fields
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    errors.push(`${field.previousElementSibling.textContent.replace(' *', '')} is required.`);
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
            
            // Check email format
            const emailField = document.getElementById('summaryEmail');
            if (emailField.value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailField.value)) {
                errors.push('Please enter a valid email address.');
                emailField.classList.add('is-invalid');
            }
            
            if (errors.length > 0) {
                errorsList.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
                summary.style.display = 'block';
            } else {
                summary.style.display = 'none';
                alert('Form is valid! All fields are correct.');
            }
        }
    </script>
</body>
</html> 