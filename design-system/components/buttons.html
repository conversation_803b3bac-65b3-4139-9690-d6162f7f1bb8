<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Buttons</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container py-5">
        <div class="row mb-5">
            <div class="col-12">
                <h1 class="text-primary mb-3">CozyWish Button Components</h1>
                <p class="text-body">All buttons use our brand color palette for consistent design across the application.</p>
            </div>
        </div>
        
        <!-- Primary Brand Buttons -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">Primary Brand Buttons</h2>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Main Action Buttons</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-3 d-md-block mb-4">
                            <button class="btn btn-primary">Primary Action</button>
                            <button class="btn btn-secondary">Secondary Action</button>
                            <button class="btn btn-light">Light Action</button>
                        </div>
                        
                        <div class="d-grid gap-3 d-md-block mb-4">
                            <button class="btn btn-outline-primary">Outline Primary</button>
                            <button class="btn btn-outline-secondary">Outline Secondary</button>
                        </div>
                        
                        <div class="d-grid gap-3 d-md-block">
                            <button class="btn btn-primary btn-lg">Large Primary</button>
                            <button class="btn btn-primary">Normal Primary</button>
                            <button class="btn btn-primary btn-sm">Small Primary</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- All Button Variants -->
        <div class="row mb-5">
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Solid Buttons</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-primary">Primary</button>
                            <button class="btn btn-secondary">Secondary</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-success">Success</button>
                            <button class="btn btn-danger">Danger</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-warning">Warning</button>
                            <button class="btn btn-info">Info</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block">
                            <button class="btn btn-light">Light</button>
                            <button class="btn btn-dark">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Outline Buttons</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-outline-primary">Primary</button>
                            <button class="btn btn-outline-secondary">Secondary</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-outline-success">Success</button>
                            <button class="btn btn-outline-danger">Danger</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block mb-3">
                            <button class="btn btn-outline-warning">Warning</button>
                            <button class="btn btn-outline-info">Info</button>
                        </div>
                        <div class="d-grid gap-2 d-md-block">
                            <button class="btn btn-outline-light">Light</button>
                            <button class="btn btn-outline-dark">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Button Sizes -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Button Sizes</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-3 d-md-block mb-4">
                            <button class="btn btn-primary btn-lg">Large Button</button>
                            <button class="btn btn-primary">Normal Button</button>
                            <button class="btn btn-primary btn-sm">Small Button</button>
                        </div>
                        
                        <div class="d-grid gap-3 d-md-block">
                            <button class="btn btn-outline-primary btn-lg">Large Outline</button>
                            <button class="btn btn-outline-primary">Normal Outline</button>
                            <button class="btn btn-outline-primary btn-sm">Small Outline</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Button States -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Button States</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-3 d-md-block mb-4">
                            <button class="btn btn-primary">Normal</button>
                            <button class="btn btn-primary" disabled>Disabled</button>
                            <button class="btn btn-primary active">Active</button>
                        </div>
                        
                        <div class="d-grid gap-3 d-md-block">
                            <button class="btn btn-outline-primary">Normal Outline</button>
                            <button class="btn btn-outline-primary" disabled>Disabled Outline</button>
                            <button class="btn btn-outline-primary active">Active Outline</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Button Groups -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Button Groups</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="btn-group mb-3" role="group">
                            <button type="button" class="btn btn-primary">Left</button>
                            <button type="button" class="btn btn-primary">Middle</button>
                            <button type="button" class="btn btn-primary">Right</button>
                        </div>
                        
                        <div class="btn-group mb-3" role="group">
                            <button type="button" class="btn btn-outline-primary">Left</button>
                            <button type="button" class="btn btn-outline-primary">Middle</button>
                            <button type="button" class="btn btn-outline-primary">Right</button>
                        </div>
                        
                        <div class="btn-group-vertical" role="group">
                            <button type="button" class="btn btn-primary">Top</button>
                            <button type="button" class="btn btn-primary">Middle</button>
                            <button type="button" class="btn btn-primary">Bottom</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Color Palette Reference -->
        <div class="row">
            <div class="col-12">
                <h3 class="text-primary mb-3">Color Palette Used</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded me-3" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Primary</strong><br>
                                        <small class="text-muted">#43251B</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded me-3" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Secondary</strong><br>
                                        <small class="text-muted">#5A342A</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded me-3 border" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Light</strong><br>
                                        <small class="text-muted">#FEF6F0</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-white rounded me-3 border" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>White</strong><br>
                                        <small class="text-muted">#FFFFFF</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 