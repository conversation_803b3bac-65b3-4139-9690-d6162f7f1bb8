<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Spacing & Sizing</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container py-5">
        <div class="row mb-5">
            <div class="col-12">
                <h1 class="text-primary mb-3">CozyWish Spacing & Sizing System</h1>
                <p class="text-body lead">Professional spacing and sizing utilities using Bootstrap 5's proven system for consistent, scalable layouts across your application.</p>
                <div class="alert alert-primary">
                    <strong>Design Principle:</strong> Consistent spacing creates visual harmony and improves user experience. This system provides a comprehensive set of utilities for all spacing and sizing needs.
                </div>
            </div>
        </div>
        
        <!-- Spacing Utilities -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">Spacing Utilities</h2>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Spacing Scale Reference</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Positive Spacing Scale</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Value</th>
                                                <th>Pixels</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>0</code></td><td>0rem</td><td>0px</td></tr>
                                            <tr><td><code>1</code></td><td>0.25rem</td><td>4px</td></tr>
                                            <tr><td><code>2</code></td><td>0.5rem</td><td>8px</td></tr>
                                            <tr><td><code>3</code></td><td>1rem</td><td>16px</td></tr>
                                            <tr><td><code>4</code></td><td>1.5rem</td><td>24px</td></tr>
                                            <tr><td><code>5</code></td><td>3rem</td><td>48px</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">Negative & Auto Spacing</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Value</th>
                                                <th>Pixels</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>auto</code></td><td>auto</td><td>auto</td></tr>
                                            <tr><td><code>n1</code></td><td>-0.25rem</td><td>-4px</td></tr>
                                            <tr><td><code>n2</code></td><td>-0.5rem</td><td>-8px</td></tr>
                                            <tr><td><code>n3</code></td><td>-1rem</td><td>-16px</td></tr>
                                            <tr><td><code>n4</code></td><td>-1.5rem</td><td>-24px</td></tr>
                                            <tr><td><code>n5</code></td><td>-3rem</td><td>-48px</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Margin Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-4">Margin Examples</h3>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Margin Utilities Demonstration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">All Sides (m)</h6>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-primary text-white m-0 p-2 rounded">m-0</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-primary text-white m-1 p-2 rounded">m-1</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-primary text-white m-2 p-2 rounded">m-2</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-primary text-white m-3 p-2 rounded">m-3</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-primary text-white m-4 p-2 rounded">m-4</div>
                                </div>
                                <div class="bg-light border rounded p-3">
                                    <div class="bg-primary text-white m-5 p-2 rounded">m-5</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Top & Bottom (my)</h6>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-secondary text-white my-0 p-2 rounded">my-0</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-secondary text-white my-1 p-2 rounded">my-1</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-secondary text-white my-2 p-2 rounded">my-2</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-secondary text-white my-3 p-2 rounded">my-3</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-secondary text-white my-4 p-2 rounded">my-4</div>
                                </div>
                                <div class="bg-light border rounded p-3">
                                    <div class="bg-secondary text-white my-5 p-2 rounded">my-5</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Left & Right (mx)</h6>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-accent-light text-primary mx-0 p-2 rounded">mx-0</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-accent-light text-primary mx-1 p-2 rounded">mx-1</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-accent-light text-primary mx-2 p-2 rounded">mx-2</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-accent-light text-primary mx-3 p-2 rounded">mx-3</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-2">
                                    <div class="bg-accent-light text-primary mx-4 p-2 rounded">mx-4</div>
                                </div>
                                <div class="bg-light border rounded p-3">
                                    <div class="bg-accent-light text-primary mx-5 p-2 rounded">mx-5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Padding Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-4">Padding Examples</h3>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Padding Utilities Demonstration</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">All Sides (p)</h6>
                                <div class="bg-primary text-white p-0 mb-2 rounded">p-0</div>
                                <div class="bg-primary text-white p-1 mb-2 rounded">p-1</div>
                                <div class="bg-primary text-white p-2 mb-2 rounded">p-2</div>
                                <div class="bg-primary text-white p-3 mb-2 rounded">p-3</div>
                                <div class="bg-primary text-white p-4 mb-2 rounded">p-4</div>
                                <div class="bg-primary text-white p-5 rounded">p-5</div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Top & Bottom (py)</h6>
                                <div class="bg-secondary text-white py-0 mb-2 rounded">py-0</div>
                                <div class="bg-secondary text-white py-1 mb-2 rounded">py-1</div>
                                <div class="bg-secondary text-white py-2 mb-2 rounded">py-2</div>
                                <div class="bg-secondary text-white py-3 mb-2 rounded">py-3</div>
                                <div class="bg-secondary text-white py-4 mb-2 rounded">py-4</div>
                                <div class="bg-secondary text-white py-5 rounded">py-5</div>
                            </div>
                            
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Left & Right (px)</h6>
                                <div class="bg-accent-light text-primary px-0 mb-2 rounded">px-0</div>
                                <div class="bg-accent-light text-primary px-1 mb-2 rounded">px-1</div>
                                <div class="bg-accent-light text-primary px-2 mb-2 rounded">px-2</div>
                                <div class="bg-accent-light text-primary px-3 mb-2 rounded">px-3</div>
                                <div class="bg-accent-light text-primary px-4 mb-2 rounded">px-4</div>
                                <div class="bg-accent-light text-primary px-5 rounded">px-5</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Individual Sides -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Individual Sides</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Margin Individual Sides</h6>
                                <div class="bg-light border p-3 mb-2">
                                    <div class="bg-primary text-white mt-3 p-2">mt-3 (top)</div>
                                </div>
                                <div class="bg-light border p-3 mb-2">
                                    <div class="bg-primary text-white mb-3 p-2">mb-3 (bottom)</div>
                                </div>
                                <div class="bg-light border p-3 mb-2">
                                    <div class="bg-primary text-white ms-3 p-2">ms-3 (start/left)</div>
                                </div>
                                <div class="bg-light border p-3 mb-2">
                                    <div class="bg-primary text-white me-3 p-2">me-3 (end/right)</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Padding Individual Sides</h6>
                                <div class="bg-secondary text-white pt-3 mb-2">pt-3 (top)</div>
                                <div class="bg-secondary text-white pb-3 mb-2">pb-3 (bottom)</div>
                                <div class="bg-secondary text-white ps-3 mb-2">ps-3 (start/left)</div>
                                <div class="bg-secondary text-white pe-3 mb-2">pe-3 (end/right)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Responsive Spacing -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Responsive Spacing</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Responsive Margin</h6>
                                <div class="bg-light border p-3 mb-2">
                                    <div class="bg-primary text-white m-1 m-md-3 m-lg-5 p-2">m-1 m-md-3 m-lg-5</div>
                                </div>
                                <p class="text-body small">Small: m-1, Medium+: m-3, Large+: m-5</p>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Responsive Padding</h6>
                                <div class="bg-secondary text-white p-1 p-md-3 p-lg-5 mb-2">p-1 p-md-3 p-lg-5</div>
                                <p class="text-body small">Small: p-1, Medium+: p-3, Large+: p-5</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Width & Height -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Width & Height Utilities</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Width Classes</h6>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white w-25 p-2">w-25 (25%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white w-50 p-2">w-50 (50%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white w-75 p-2">w-75 (75%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white w-100 p-2">w-100 (100%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white w-auto p-2">w-auto</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Height Classes</h6>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-secondary text-white h-25 p-2">h-25 (25%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-secondary text-white h-50 p-2">h-50 (50%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-secondary text-white h-75 p-2">h-75 (75%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-secondary text-white h-100 p-2">h-100 (100%)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-secondary text-white h-auto p-2">h-auto</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Viewport Height & Width -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Viewport Sizing</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Viewport Width</h6>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-accent-light text-primary vw-25 p-2">vw-25 (25vw)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-accent-light text-primary vw-50 p-2">vw-50 (50vw)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-accent-light text-primary vw-75 p-2">vw-75 (75vw)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-accent-light text-primary vw-100 p-2">vw-100 (100vw)</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <h6 class="text-primary mb-3">Viewport Height</h6>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white vh-25 p-2">vh-25 (25vh)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white vh-50 p-2">vh-50 (50vh)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white vh-75 p-2">vh-75 (75vh)</div>
                                </div>
                                <div class="bg-light border p-2 mb-2">
                                    <div class="bg-primary text-white vh-100 p-2">vh-100 (100vh)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Practical Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Practical Spacing Examples</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">Card Spacing</h6>
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Card Title</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-body mb-3">Card content with proper spacing.</p>
                                        <button class="btn btn-primary">Action</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">Form Spacing</h6>
                                <form>
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" placeholder="Enter email">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Password</label>
                                        <input type="password" class="form-control" placeholder="Password">
                                    </div>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </form>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">List Spacing</h6>
                                <ul class="list-group">
                                    <li class="list-group-item">First item</li>
                                    <li class="list-group-item">Second item</li>
                                    <li class="list-group-item">Third item</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Auto Margins -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Auto Margins</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">Horizontal Centering</h6>
                                <div class="bg-light border p-3">
                                    <div class="bg-primary text-white mx-auto p-2" style="width: 200px;">mx-auto</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">Push to Right</h6>
                                <div class="bg-light border p-3">
                                    <div class="bg-secondary text-white ms-auto p-2" style="width: 200px;">ms-auto</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6 class="text-primary mb-3">Push to Left</h6>
                                <div class="bg-light border p-3">
                                    <div class="bg-accent-light text-primary me-auto p-2" style="width: 200px;">me-auto</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Negative Margins -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-4">Negative Margins</h3>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Negative Margin Utilities</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Negative Margin Examples</h6>
                                <div class="bg-light border rounded p-3 mb-3">
                                    <div class="bg-primary text-white p-2 rounded">Normal element</div>
                                    <div class="bg-secondary text-white p-2 mt-n3 rounded">mt-n3 (negative top)</div>
                                </div>
                                <div class="bg-light border rounded p-3 mb-3">
                                    <div class="bg-primary text-white p-2 rounded">Normal element</div>
                                    <div class="bg-secondary text-white p-2 mb-n3 rounded">mb-n3 (negative bottom)</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Negative Margin Scale</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="bg-light">
                                            <tr>
                                                <th>Class</th>
                                                <th>Value</th>
                                                <th>Pixels</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td><code>n1</code></td><td>-0.25rem</td><td>-4px</td></tr>
                                            <tr><td><code>n2</code></td><td>-0.5rem</td><td>-8px</td></tr>
                                            <tr><td><code>n3</code></td><td>-1rem</td><td>-16px</td></tr>
                                            <tr><td><code>n4</code></td><td>-1.5rem</td><td>-24px</td></tr>
                                            <tr><td><code>n5</code></td><td>-3rem</td><td>-48px</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Best Practices & Guidelines -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-4">Best Practices & Guidelines</h3>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Professional Spacing Guidelines</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Spacing Principles</h6>
                                <ul class="text-body">
                                    <li><strong>Consistency:</strong> Use the same spacing scale throughout your application</li>
                                    <li><strong>Hierarchy:</strong> Use larger spacing for major sections, smaller for details</li>
                                    <li><strong>Readability:</strong> Ensure adequate spacing between text elements</li>
                                    <li><strong>Responsive:</strong> Adjust spacing for different screen sizes</li>
                                </ul>
                            </div>
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary mb-3">Common Use Cases</h6>
                                <ul class="text-body">
                                    <li><strong>m-3:</strong> Standard section spacing</li>
                                    <li><strong>p-3:</strong> Card and container padding</li>
                                    <li><strong>my-4:</strong> Page section separation</li>
                                    <li><strong>px-4:</strong> Form and content padding</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Reference -->
        <div class="row">
            <div class="col-12">
                <h3 class="text-primary mb-4">Quick Reference</h3>
                <div class="card shadow-brand">
                    <div class="card-header bg-light">
                        <h5 class="mb-0 text-primary">Spacing & Sizing Quick Reference</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Spacing Scale</h6>
                                <div class="bg-light p-3 rounded">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-body">0</span>
                                        <span class="text-muted">0px</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-body">1</span>
                                        <span class="text-muted">4px</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-body">2</span>
                                        <span class="text-muted">8px</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-body">3</span>
                                        <span class="text-muted">16px</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="text-body">4</span>
                                        <span class="text-muted">24px</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span class="text-body">5</span>
                                        <span class="text-muted">48px</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Common Utilities</h6>
                                <div class="bg-light p-3 rounded">
                                    <div class="mb-2"><code>m-3</code> - Standard margin</div>
                                    <div class="mb-2"><code>p-3</code> - Standard padding</div>
                                    <div class="mb-2"><code>my-4</code> - Vertical spacing</div>
                                    <div class="mb-2"><code>px-4</code> - Horizontal padding</div>
                                    <div class="mb-2"><code>mx-auto</code> - Center element</div>
                                    <div><code>w-100</code> - Full width</div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <h6 class="text-primary mb-3">Responsive Breakpoints</h6>
                                <div class="bg-light p-3 rounded">
                                    <div class="mb-2"><code>sm:</code> ≥576px</div>
                                    <div class="mb-2"><code>md:</code> ≥768px</div>
                                    <div class="mb-2"><code>lg:</code> ≥992px</div>
                                    <div class="mb-2"><code>xl:</code> ≥1200px</div>
                                    <div class="mb-2"><code>xxl:</code> ≥1400px</div>
                                    <div><strong>Example:</strong> m-2 m-md-4</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 