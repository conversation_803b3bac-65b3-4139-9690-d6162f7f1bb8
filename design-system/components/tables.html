<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Professional Tables</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container-fluid py-5">
        <!-- Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="text-primary mb-3 fw-bold">CozyWish Professional Table Components</h1>
                    <p class="text-body lead">Comprehensive, responsive table components with advanced features for modern data presentation.</p>
                </div>
            </div>
        </div>

        <!-- Basic Tables Section -->
        <section class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-primary mb-4 fw-semibold">
                        <i class="bi bi-table me-2"></i>Basic Table Styles
                    </h2>
                    
                    <!-- Basic Professional Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-grid-3x3-gap me-2"></i>Professional Basic Table
                            </h5>
                            <span class="badge bg-primary">Standard</span>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>
                                                <i class="bi bi-person me-2"></i>Name
                                            </th>
                                            <th>
                                                <i class="bi bi-envelope me-2"></i>Email
                                            </th>
                                            <th>
                                                <i class="bi bi-shield-check me-2"></i>Role
                                            </th>
                                            <th>
                                                <i class="bi bi-activity me-2"></i>Status
                                            </th>
                                            <th class="text-center">
                                                <i class="bi bi-gear me-2"></i>Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-medium">John Doe</td>
                                            <td><EMAIL></td>
                                            <td><span class="badge bg-dark">Administrator</span></td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Jane Smith</td>
                                            <td><EMAIL></td>
                                            <td><span class="badge bg-secondary">Editor</span></td>
                                            <td><span class="badge bg-warning">Pending</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Bob Johnson</td>
                                            <td><EMAIL></td>
                                            <td><span class="badge bg-info">User</span></td>
                                            <td><span class="badge bg-danger">Inactive</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Striped Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-stripe me-2"></i>Striped Table
                            </h5>
                            <span class="badge bg-secondary">Striped</span>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>
                                                <i class="bi bi-box me-2"></i>Product
                                            </th>
                                            <th>
                                                <i class="bi bi-tags me-2"></i>Category
                                            </th>
                                            <th>
                                                <i class="bi bi-currency-dollar me-2"></i>Price
                                            </th>
                                            <th>
                                                <i class="bi bi-box-seam me-2"></i>Stock
                                            </th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-medium">MacBook Pro 16"</td>
                                            <td>Electronics</td>
                                            <td class="fw-bold text-success">$2,499.00</td>
                                            <td><span class="badge bg-success">25 in stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Wireless Mouse</td>
                                            <td>Accessories</td>
                                            <td class="fw-bold text-success">$29.99</td>
                                            <td><span class="badge bg-success">150 in stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Mechanical Keyboard</td>
                                            <td>Accessories</td>
                                            <td class="fw-bold text-success">$149.99</td>
                                            <td><span class="badge bg-warning">5 left</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">4K Monitor</td>
                                            <td>Electronics</td>
                                            <td class="fw-bold text-success">$599.99</td>
                                            <td><span class="badge bg-danger">Out of stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Features Section -->
        <section class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-primary mb-4 fw-semibold">
                        <i class="bi bi-lightning me-2"></i>Advanced Table Features
                    </h2>

                    <!-- Sortable Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-sort-down me-2"></i>Sortable Columns
                            </h5>
                            <span class="badge bg-primary">Interactive</span>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="sortableTable">
                                    <thead>
                                        <tr>
                                            <th class="sortable" data-sort="name">
                                                Name <i class="bi bi-arrow-down-up ms-1"></i>
                                            </th>
                                            <th class="sortable" data-sort="age">
                                                Age <i class="bi bi-arrow-down-up ms-1"></i>
                                            </th>
                                            <th class="sortable" data-sort="salary">
                                                Salary <i class="bi bi-arrow-down-up ms-1"></i>
                                            </th>
                                            <th class="sortable" data-sort="department">
                                                Department <i class="bi bi-arrow-down-up ms-1"></i>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-medium">Alex Turner</td>
                                            <td>28</td>
                                            <td class="fw-bold text-success">$75,000</td>
                                            <td><span class="badge bg-primary">Engineering</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Emma Wilson</td>
                                            <td>32</td>
                                            <td class="fw-bold text-success">$82,000</td>
                                            <td><span class="badge bg-success">Marketing</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">David Lee</td>
                                            <td>25</td>
                                            <td class="fw-bold text-success">$68,000</td>
                                            <td><span class="badge bg-info">Sales</span></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-medium">Maria Garcia</td>
                                            <td>35</td>
                                            <td class="fw-bold text-success">$95,000</td>
                                            <td><span class="badge bg-primary">Engineering</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Searchable and Filterable Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>Search & Filter Table
                            </h5>
                            <span class="badge bg-success">Advanced</span>
                        </div>
                        <div class="card-body">
                            <!-- Search and Filter Controls -->
                            <div class="table-filters">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="searchInput" class="form-label fw-medium">
                                            <i class="bi bi-search me-1"></i>Search Products
                                        </label>
                                        <div class="search-input-wrapper">
                                            <i class="bi bi-search search-icon"></i>
                                            <input type="text" class="form-control" id="searchInput" placeholder="Search by name, category, or SKU...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="categoryFilter" class="form-label fw-medium">
                                            <i class="bi bi-tags me-1"></i>Category
                                        </label>
                                        <select class="form-select" id="categoryFilter">
                                            <option value="">All Categories</option>
                                            <option value="audio">Audio</option>
                                            <option value="accessories">Accessories</option>
                                            <option value="electronics">Electronics</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="stockFilter" class="form-label fw-medium">
                                            <i class="bi bi-box-seam me-1"></i>Stock Status
                                        </label>
                                        <select class="form-select" id="stockFilter">
                                            <option value="">All Stock</option>
                                            <option value="yes">In Stock</option>
                                            <option value="no">Out of Stock</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="searchableTable">
                                    <thead>
                                        <tr>
                                            <th>Product Name</th>
                                            <th>Category</th>
                                            <th>SKU</th>
                                            <th>Price</th>
                                            <th>Stock Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr data-category="audio" data-stock="yes">
                                            <td class="fw-medium">Premium Wireless Headphones</td>
                                            <td><span class="badge bg-purple">Audio</span></td>
                                            <td class="font-monospace">WH-001</td>
                                            <td class="fw-bold text-success">$199.99</td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-category="audio" data-stock="yes">
                                            <td class="fw-medium">Bluetooth Speaker Pro</td>
                                            <td><span class="badge bg-purple">Audio</span></td>
                                            <td class="font-monospace">BS-002</td>
                                            <td class="fw-bold text-success">$89.50</td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-category="accessories" data-stock="no">
                                            <td class="fw-medium">USB-C Cable Premium</td>
                                            <td><span class="badge bg-secondary">Accessories</span></td>
                                            <td class="font-monospace">UC-003</td>
                                            <td class="fw-bold text-success">$24.99</td>
                                            <td><span class="badge bg-danger">Out of Stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr data-category="accessories" data-stock="yes">
                                            <td class="fw-medium">Smartphone Case Elite</td>
                                            <td><span class="badge bg-secondary">Accessories</span></td>
                                            <td class="font-monospace">PC-004</td>
                                            <td class="fw-bold text-success">$39.99</td>
                                            <td><span class="badge bg-success">In Stock</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-check2-all me-2"></i>Bulk Actions & Selection
                            </h5>
                            <span class="badge bg-warning">Bulk Operations</span>
                        </div>
                        <div class="card-body">
                            <!-- Bulk Actions Bar -->
                            <div class="bulk-actions" id="bulkActionsBar">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                            <label class="form-check-label fw-medium" for="selectAll">
                                                Select All Items
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-primary btn-sm me-2" id="bulkEdit" disabled>
                                            <i class="bi bi-pencil-square me-1"></i>Edit Selected
                                        </button>
                                        <button class="btn btn-success btn-sm me-2" id="bulkExport" disabled>
                                            <i class="bi bi-download me-1"></i>Export
                                        </button>
                                        <button class="btn btn-danger btn-sm" id="bulkDelete" disabled>
                                            <i class="bi bi-trash me-1"></i>Delete Selected
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="bulkActionTable">
                                    <thead>
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" class="form-check-input" id="headerCheckbox">
                                            </th>
                                            <th>Customer Name</th>
                                            <th>Email Address</th>
                                            <th>Phone Number</th>
                                            <th>Subscription</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input row-checkbox">
                                            </td>
                                            <td class="fw-medium">John Smith</td>
                                            <td><EMAIL></td>
                                            <td>(*************</td>
                                            <td><span class="badge bg-success">Premium</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="Message">
                                                        <i class="bi bi-chat-dots"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input row-checkbox">
                                            </td>
                                            <td class="fw-medium">Sarah Johnson</td>
                                            <td><EMAIL></td>
                                            <td>(555) 234-5678</td>
                                            <td><span class="badge bg-primary">Standard</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="Message">
                                                        <i class="bi bi-chat-dots"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input row-checkbox">
                                            </td>
                                            <td class="fw-medium">Mike Davis</td>
                                            <td><EMAIL></td>
                                            <td>(555) 345-6789</td>
                                            <td><span class="badge bg-secondary">Basic</span></td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="Message">
                                                        <i class="bi bi-chat-dots"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Table States Section -->
        <section class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-primary mb-4 fw-semibold">
                        <i class="bi bi-diagram-3 me-2"></i>Table States & Conditions
                    </h2>

                    <!-- Loading State -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-arrow-clockwise me-2"></i>Loading State
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleLoadingState()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Toggle State
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-loading" id="loadingTable">
                                <div class="loading-spinner">
                                    <div class="spinner-border text-primary mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <h5 class="text-muted">Loading data...</h5>
                                    <p class="text-muted">Please wait while we fetch your information.</p>
                                </div>
                            </div>
                            <div class="table-responsive d-none" id="loadedTableContainer">
                                <table class="table table-hover mb-0" id="loadedTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Item Name</th>
                                            <th>Status</th>
                                            <th>Last Updated</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="font-monospace">#001</td>
                                            <td class="fw-medium">Loaded Item 1</td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>2024-01-17 10:30</td>
                                        </tr>
                                        <tr>
                                            <td class="font-monospace">#002</td>
                                            <td class="fw-medium">Loaded Item 2</td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>2024-01-17 11:15</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-inbox me-2"></i>Empty State
                            </h5>
                            <button class="btn btn-sm btn-outline-secondary" onclick="toggleEmptyState()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Toggle State
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-empty" id="emptyTable">
                                <div class="empty-state">
                                    <i class="bi bi-inbox display-1 text-muted mb-3"></i>
                                    <h4 class="text-muted mb-3">No Data Available</h4>
                                    <p class="text-muted mb-4">There are no items to display at the moment. Get started by adding your first item.</p>
                                    <button class="btn btn-primary">
                                        <i class="bi bi-plus-circle me-2"></i>Add New Item
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive d-none" id="populatedTableContainer">
                                <table class="table table-hover mb-0" id="populatedTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Created Date</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-medium">Sample Item</td>
                                            <td>This is a sample item with description</td>
                                            <td>2024-01-17</td>
                                            <td class="text-center">
                                                <button class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-pencil-square me-1"></i>Edit
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>Error State
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-error">
                                <div class="error-state">
                                    <i class="bi bi-exclamation-triangle display-1 text-danger mb-3"></i>
                                    <h4 class="text-danger mb-3">Error Loading Data</h4>
                                    <p class="text-muted mb-4">Unable to load the requested data. This might be due to a network issue or server error.</p>
                                    <div class="d-flex gap-2 justify-content-center">
                                        <button class="btn btn-primary">
                                            <i class="bi bi-arrow-clockwise me-2"></i>Retry
                                        </button>
                                        <button class="btn btn-outline-secondary">
                                            <i class="bi bi-question-circle me-2"></i>Get Help
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Results State -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-search me-2"></i>No Search Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="search-input-wrapper">
                                    <i class="bi bi-search search-icon"></i>
                                    <input type="text" class="form-control" id="noResultsSearch" placeholder="Search for something that doesn't exist..." value="nonexistentitem">
                                </div>
                            </div>
                            <div class="table-no-results">
                                <div class="no-results-state">
                                    <i class="bi bi-search display-1 text-muted mb-3"></i>
                                    <h4 class="text-muted mb-3">No Results Found</h4>
                                    <p class="text-muted mb-4">We couldn't find any items matching "<strong>nonexistentitem</strong>". Try adjusting your search terms or filters.</p>
                                    <div class="d-flex gap-2 justify-content-center">
                                        <button class="btn btn-outline-primary">
                                            <i class="bi bi-x-circle me-2"></i>Clear Search
                                        </button>
                                        <button class="btn btn-outline-secondary">
                                            <i class="bi bi-funnel me-2"></i>Reset Filters
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Responsive Tables Section -->
        <section class="mb-5">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-primary mb-4 fw-semibold">
                        <i class="bi bi-phone me-2"></i>Responsive Table Designs
                    </h2>

                    <!-- Horizontal Scroll Table -->
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-arrows-expand me-2"></i>Horizontal Scroll Table
                            </h5>
                            <small class="text-muted">Ideal for tables with many columns</small>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Product ID</th>
                                            <th>Product Name</th>
                                            <th>Category</th>
                                            <th>Brand</th>
                                            <th>SKU</th>
                                            <th>Price</th>
                                            <th>Cost</th>
                                            <th>Margin</th>
                                            <th>Stock</th>
                                            <th>Status</th>
                                            <th>Created Date</th>
                                            <th>Last Updated</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="font-monospace">P001</td>
                                            <td class="fw-medium">Wireless Bluetooth Headphones Premium</td>
                                            <td><span class="badge bg-purple">Audio Equipment</span></td>
                                            <td>TechBrand Pro</td>
                                            <td class="font-monospace">WBH-001</td>
                                            <td class="fw-bold text-success">$189.99</td>
                                            <td class="text-muted">$95.00</td>
                                            <td class="fw-bold text-success">50%</td>
                                            <td><span class="badge bg-success">150</span></td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>2024-01-01</td>
                                            <td>2024-01-15</td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="font-monospace">P002</td>
                                            <td class="fw-medium">Smartphone Case Premium Protection</td>
                                            <td><span class="badge bg-secondary">Phone Accessories</span></td>
                                            <td>CasePro Elite</td>
                                            <td class="font-monospace">SCP-002</td>
                                            <td class="fw-bold text-success">$49.99</td>
                                            <td class="text-muted">$15.50</td>
                                            <td class="fw-bold text-success">69%</td>
                                            <td><span class="badge bg-success">300</span></td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>2024-01-05</td>
                                            <td>2024-01-12</td>
                                            <td class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil-square"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile-Optimized Stacked Table -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-stack me-2"></i>Mobile-Optimized Stacked Table
                            </h5>
                            <small class="text-muted">Automatically stacks on mobile devices</small>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover table-stacked mb-0">
                                    <thead>
                                        <tr>
                                            <th>Customer</th>
                                            <th>Order Details</th>
                                            <th>Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td data-label="Customer">
                                                <div>
                                                    <strong class="fw-bold">John Doe</strong><br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i><EMAIL>
                                                    </small>
                                                </div>
                                            </td>
                                            <td data-label="Order Details">
                                                <div>
                                                    <strong class="fw-bold">Order #12345</strong><br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-currency-dollar me-1"></i>$250.00 • 
                                                        <i class="bi bi-box me-1"></i>5 items
                                                    </small>
                                                </div>
                                            </td>
                                            <td data-label="Status">
                                                <span class="badge bg-success">Shipped</span>
                                            </td>
                                            <td data-label="Actions" class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye me-1"></i>View
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-truck me-1"></i>Track
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Customer">
                                                <div>
                                                    <strong class="fw-bold">Jane Smith</strong><br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i><EMAIL>
                                                    </small>
                                                </div>
                                            </td>
                                            <td data-label="Order Details">
                                                <div>
                                                    <strong class="fw-bold">Order #12346</strong><br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-currency-dollar me-1"></i>$175.99 • 
                                                        <i class="bi bi-box me-1"></i>3 items
                                                    </small>
                                                </div>
                                            </td>
                                            <td data-label="Status">
                                                <span class="badge bg-warning">Processing</span>
                                            </td>
                                            <td data-label="Actions" class="text-center">
                                                <div class="btn-group-action">
                                                    <button class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye me-1"></i>View
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary">
                                                        <i class="bi bi-clock me-1"></i>Pending
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Color Palette Reference -->
        <section>
            <div class="row">
                <div class="col-12">
                    <h3 class="text-primary mb-4 fw-semibold">
                        <i class="bi bi-palette me-2"></i>Table Color Palette Reference
                    </h3>
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-lg-3 col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded me-3" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <strong class="fw-bold">Primary</strong><br>
                                            <small class="text-muted font-monospace">#43251B</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-secondary rounded me-3" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <strong class="fw-bold">Secondary</strong><br>
                                            <small class="text-muted font-monospace">#5A342A</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success rounded me-3" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <strong class="fw-bold">Success</strong><br>
                                            <small class="text-muted font-monospace">#2D5A3D</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-light rounded me-3 border" style="width: 40px; height: 40px;"></div>
                                        <div>
                                            <strong class="fw-bold">Light</strong><br>
                                            <small class="text-muted font-monospace">#FEF6F0</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Enhanced JavaScript for professional table functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            // Enhanced sortable table functionality
            initializeSortableTable();
            
            // Search and filter functionality
            initializeSearchAndFilter();
            
            // Bulk actions functionality
            initializeBulkActions();
            
            // Initialize tooltips for action buttons
            initializeTooltips();
        });

        function initializeSortableTable() {
            const sortableHeaders = document.querySelectorAll('.sortable');
            
            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.dataset.sort;
                    const table = this.closest('table');
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    // Toggle sort direction
                    const isAscending = this.classList.contains('asc');
                    
                    // Remove all sort classes and reset icons
                    sortableHeaders.forEach(h => {
                        h.classList.remove('asc', 'desc');
                        const icon = h.querySelector('i');
                        if (icon) {
                            icon.className = 'bi bi-arrow-down-up ms-1';
                        }
                    });
                    
                    // Add sort class and update icon
                    this.classList.add(isAscending ? 'desc' : 'asc');
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = isAscending ? 'bi bi-sort-down ms-1' : 'bi bi-sort-up ms-1';
                    }
                    
                    // Sort rows based on column type
                    rows.sort((a, b) => {
                        const aValue = a.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
                        const bValue = b.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
                        
                        let comparison = 0;
                        
                        if (column === 'salary' || column === 'age') {
                            const aNum = parseFloat(aValue.replace(/[^0-9.-]+/g, ''));
                            const bNum = parseFloat(bValue.replace(/[^0-9.-]+/g, ''));
                            comparison = aNum - bNum;
                        } else {
                            comparison = aValue.localeCompare(bValue);
                        }
                        
                        return isAscending ? -comparison : comparison;
                    });
                    
                    // Reorder rows with smooth animation
                    rows.forEach((row, index) => {
                        setTimeout(() => {
                            tbody.appendChild(row);
                        }, index * 50);
                    });
                });
            });
            
            function getColumnIndex(column) {
                const columnMap = { 
                    name: 1, 
                    age: 2, 
                    salary: 3, 
                    department: 4 
                };
                return columnMap[column] || 1;
            }
        }

        function initializeSearchAndFilter() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const stockFilter = document.getElementById('stockFilter');
            const searchableRows = document.querySelectorAll('#searchableTable tbody tr');
            
            function performFilter() {
                const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
                const categoryValue = categoryFilter ? categoryFilter.value : '';
                const stockValue = stockFilter ? stockFilter.value : '';
                
                searchableRows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    const category = row.dataset.category || '';
                    const stock = row.dataset.stock || '';
                    
                    const searchMatch = !searchTerm || text.includes(searchTerm);
                    const categoryMatch = !categoryValue || category === categoryValue;
                    const stockMatch = !stockValue || stock === stockValue;
                    
                    const shouldShow = searchMatch && categoryMatch && stockMatch;
                    row.style.display = shouldShow ? '' : 'none';
                    
                    // Add highlight animation for matching rows
                    if (shouldShow && searchTerm) {
                        row.classList.add('table-warning');
                        setTimeout(() => row.classList.remove('table-warning'), 2000);
                    }
                });
            }
            
            if (searchInput) {
                searchInput.addEventListener('input', performFilter);
            }
            if (categoryFilter) {
                categoryFilter.addEventListener('change', performFilter);
            }
            if (stockFilter) {
                stockFilter.addEventListener('change', performFilter);
            }
        }

        function initializeBulkActions() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const headerCheckbox = document.getElementById('headerCheckbox');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const bulkEditBtn = document.getElementById('bulkEdit');
            const bulkDeleteBtn = document.getElementById('bulkDelete');
            const bulkExportBtn = document.getElementById('bulkExport');
            const bulkActionsBar = document.getElementById('bulkActionsBar');
            
            function updateBulkButtons() {
                const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
                const hasSelection = checkedBoxes.length > 0;
                
                // Update button states
                if (bulkEditBtn) bulkEditBtn.disabled = !hasSelection;
                if (bulkDeleteBtn) bulkDeleteBtn.disabled = !hasSelection;
                if (bulkExportBtn) bulkExportBtn.disabled = !hasSelection;
                
                // Update bulk actions bar appearance
                if (bulkActionsBar) {
                    bulkActionsBar.classList.toggle('active', hasSelection);
                }
                
                // Highlight selected rows
                document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                    const row = checkbox.closest('tr');
                    row.classList.toggle('selected', checkbox.checked);
                });
            }
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    if (headerCheckbox) {
                        headerCheckbox.checked = this.checked;
                    }
                    updateBulkButtons();
                });
            }
            
            if (headerCheckbox) {
                headerCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = this.checked;
                    }
                    updateBulkButtons();
                });
            }
            
            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkButtons();
                    
                    // Update "select all" checkboxes based on individual selections
                    const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
                    const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);
                    
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = allChecked;
                        selectAllCheckbox.indeterminate = someChecked && !allChecked;
                    }
                    if (headerCheckbox) {
                        headerCheckbox.checked = allChecked;
                        headerCheckbox.indeterminate = someChecked && !allChecked;
                    }
                });
            });
        }

        function initializeTooltips() {
            // Initialize Bootstrap tooltips for action buttons
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Loading state toggle with enhanced animation
        function toggleLoadingState() {
            const loadingDiv = document.getElementById('loadingTable');
            const tableContainer = document.getElementById('loadedTableContainer');
            
            if (loadingDiv && tableContainer) {
                if (loadingDiv.classList.contains('d-none')) {
                    // Show loading
                    tableContainer.classList.add('d-none');
                    loadingDiv.classList.remove('d-none');
                } else {
                    // Show table with delay to simulate loading
                    setTimeout(() => {
                        loadingDiv.classList.add('d-none');
                        tableContainer.classList.remove('d-none');
                    }, 1500);
                }
            }
        }
        
        // Empty state toggle with smooth transition
        function toggleEmptyState() {
            const emptyDiv = document.getElementById('emptyTable');
            const tableContainer = document.getElementById('populatedTableContainer');
            
            if (emptyDiv && tableContainer) {
                if (emptyDiv.classList.contains('d-none')) {
                    tableContainer.classList.add('d-none');
                    emptyDiv.classList.remove('d-none');
                } else {
                    emptyDiv.classList.add('d-none');
                    tableContainer.classList.remove('d-none');
                }
            }
        }

        // Enhanced row hover effects
        document.addEventListener('DOMContentLoaded', function() {
            const tableRows = document.querySelectorAll('.table tbody tr');
            
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>

    <style>
        /* Additional professional styling */
        .bg-purple {
            background-color: #6f42c1 !important;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr.selected {
            background-color: rgba(13, 110, 253, 0.1);
            border-left: 4px solid var(--bs-primary);
        }
        
        .bulk-actions.active {
            animation: slideIn 0.3s ease-in-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .btn-group-action .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
        
        .search-icon {
            pointer-events: none;
        }
        
        .font-monospace {
            font-family: 'Courier New', monospace !important;
        }
    </style>
</body>
</html> 