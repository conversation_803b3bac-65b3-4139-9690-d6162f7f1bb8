<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Brand Color Palette</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- CozyWish Design System -->
  <link rel="stylesheet" href="../cozywish.css">
</head>
<body style="background: #F8F9FA;">
  <div class="container py-4">
    <h2 class="mb-4" style="color: #43251B;">Brand Color Palette</h2>
    
    <div class="palette-container">
      <div class="palette-row">
        <div>
          <div class="color-swatch swatch-primary"></div>
          <div class="swatch-label">Primary</div>
          <div class="swatch-hex">#43251B</div>
          <div class="swatch-usage">Buttons, links, titles</div>
        </div>
        <div>
          <div class="color-swatch swatch-primary-light"></div>
          <div class="swatch-label">Primary Light</div>
          <div class="swatch-hex">#5A342A</div>
          <div class="swatch-usage">Hover states, cards, badges</div>
        </div>
        <div>
          <div class="color-swatch swatch-accent-light"></div>
          <div class="swatch-label">Accent Light</div>
          <div class="swatch-hex">#FEF6F0</div>
          <div class="swatch-usage">Card backgrounds, sections</div>
        </div>
        <div>
          <div class="color-swatch swatch-background"></div>
          <div class="swatch-label">Page BG</div>
          <div class="swatch-hex">#F8F9FA</div>
          <div class="swatch-usage">Main app/page background</div>
        </div>
      </div>
      
      <div class="palette-row">
        <div>
          <div class="color-swatch swatch-white"></div>
          <div class="swatch-label">White</div>
          <div class="swatch-hex">#FFFFFF</div>
          <div class="swatch-usage">Card contrast, overlays</div>
        </div>
        <div>
          <div class="color-swatch swatch-black"></div>
          <div class="swatch-label">Black</div>
          <div class="swatch-hex">#222222</div>
          <div class="swatch-usage">Main text color</div>
        </div>
        <div>
          <div class="color-swatch swatch-border"></div>
          <div class="swatch-label">Border</div>
          <div class="swatch-hex">#E0DFDE</div>
          <div class="swatch-usage">Card borders, shadows</div>
        </div>
      </div>
    </div>

    <!-- Semantic Colors Section -->
    <div class="palette-container">
      <h3 class="mb-3" style="color: #43251B;">Semantic Colors</h3>
      <div class="palette-row">
        <div>
          <div class="color-swatch swatch-success"></div>
          <div class="swatch-label">Success</div>
          <div class="swatch-hex">#2D5A3D</div>
          <div class="swatch-usage">Success states, confirmations</div>
        </div>
        <div>
          <div class="color-swatch swatch-success-light"></div>
          <div class="swatch-label">Success Light</div>
          <div class="swatch-hex">#E8F5E8</div>
          <div class="swatch-usage">Success backgrounds, alerts</div>
        </div>
        <div>
          <div class="color-swatch swatch-warning"></div>
          <div class="swatch-label">Warning</div>
          <div class="swatch-hex">#B8860B</div>
          <div class="swatch-usage">Warnings, attention needed</div>
        </div>
        <div>
          <div class="color-swatch swatch-warning-light"></div>
          <div class="swatch-label">Warning Light</div>
          <div class="swatch-hex">#FFF8E1</div>
          <div class="swatch-usage">Warning backgrounds, alerts</div>
        </div>
      </div>
      
      <div class="palette-row">
        <div>
          <div class="color-swatch swatch-error"></div>
          <div class="swatch-label">Error</div>
          <div class="swatch-hex">#8B2635</div>
          <div class="swatch-usage">Errors, critical issues</div>
        </div>
        <div>
          <div class="color-swatch swatch-error-light"></div>
          <div class="swatch-label">Error Light</div>
          <div class="swatch-hex">#FDE8E8</div>
          <div class="swatch-usage">Error backgrounds, alerts</div>
        </div>
        <div>
          <div class="color-swatch swatch-info"></div>
          <div class="swatch-label">Info</div>
          <div class="swatch-hex">#2C5F8A</div>
          <div class="swatch-usage">Information, tips, help</div>
        </div>
        <div>
          <div class="color-swatch swatch-info-light"></div>
          <div class="swatch-label">Info Light</div>
          <div class="swatch-hex">#E3F2FD</div>
          <div class="swatch-usage">Info backgrounds, alerts</div>
        </div>
      </div>
    </div>

    <div class="color-table">
      <h3 class="mb-3" style="color: #43251B;">Color Reference Table</h3>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Purpose</th>
            <th>Name</th>
            <th>HEX</th>
            <th>Usage Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Primary</td>
            <td>Primary</td>
            <td><code>#43251B</code></td>
            <td>Buttons, links, titles</td>
          </tr>
          <tr>
            <td>Primary Light</td>
            <td>Primary Light</td>
            <td><code>#5A342A</code></td>
            <td>Hover states, cards, badges</td>
          </tr>
          <tr>
            <td>Accent Light</td>
            <td>Accent Light</td>
            <td><code>#FEF6F0</code></td>
            <td>Card backgrounds, sections</td>
          </tr>
          <tr>
            <td>Background</td>
            <td>Page BG</td>
            <td><code>#F8F9FA</code></td>
            <td>Main app/page background</td>
          </tr>
          <tr>
            <td>White</td>
            <td>White</td>
            <td><code>#FFFFFF</code></td>
            <td>Card contrast, overlays</td>
          </tr>
          <tr>
            <td>Black (text)</td>
            <td>Black</td>
            <td><code>#222222</code></td>
            <td>Main text color</td>
          </tr>
          <tr>
            <td>Border/Shadow</td>
            <td>Border</td>
            <td><code>#E0DFDE</code></td>
            <td>Card borders, shadows</td>
          </tr>
          <tr style="background: #E8F5E8;">
            <td>Success</td>
            <td>Success</td>
            <td><code>#2D5A3D</code></td>
            <td>Success states, confirmations</td>
          </tr>
          <tr style="background: #E8F5E8;">
            <td>Success Light</td>
            <td>Success Light</td>
            <td><code>#E8F5E8</code></td>
            <td>Success backgrounds, alerts</td>
          </tr>
          <tr style="background: #FFF8E1;">
            <td>Warning</td>
            <td>Warning</td>
            <td><code>#B8860B</code></td>
            <td>Warnings, attention needed</td>
          </tr>
          <tr style="background: #FFF8E1;">
            <td>Warning Light</td>
            <td>Warning Light</td>
            <td><code>#FFF8E1</code></td>
            <td>Warning backgrounds, alerts</td>
          </tr>
          <tr style="background: #FDE8E8;">
            <td>Error</td>
            <td>Error</td>
            <td><code>#8B2635</code></td>
            <td>Errors, critical issues</td>
          </tr>
          <tr style="background: #FDE8E8;">
            <td>Error Light</td>
            <td>Error Light</td>
            <td><code>#FDE8E8</code></td>
            <td>Error backgrounds, alerts</td>
          </tr>
          <tr style="background: #E3F2FD;">
            <td>Info</td>
            <td>Info</td>
            <td><code>#2C5F8A</code></td>
            <td>Information, tips, help</td>
          </tr>
          <tr style="background: #E3F2FD;">
            <td>Info Light</td>
            <td>Info Light</td>
            <td><code>#E3F2FD</code></td>
            <td>Info backgrounds, alerts</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 