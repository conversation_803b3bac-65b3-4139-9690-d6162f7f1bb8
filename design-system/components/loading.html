<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Professional Loading Components</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container py-5">
        <!-- Header Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-arrow-clockwise text-primary me-3 fs-1"></i>
                    <div>
                        <h1 class="text-primary mb-1">Professional Loading Components</h1>
                        <p class="text-muted mb-0">Enterprise-grade loading indicators, progress bars, and skeleton loaders</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-0 bg-primary bg-opacity-10">
                            <div class="card-body text-center py-3">
                                <i class="bi bi-speedometer2 text-primary fs-3 mb-2"></i>
                                <h6 class="text-primary mb-1">High Performance</h6>
                                <small class="text-muted">Optimized animations</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-success bg-opacity-10">
                            <div class="card-body text-center py-3">
                                <i class="bi bi-universal-access text-success fs-3 mb-2"></i>
                                <h6 class="text-success mb-1">Accessible</h6>
                                <small class="text-muted">WCAG compliant</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-accent-light">
                            <div class="card-body text-center py-3">
                                <i class="bi bi-phone text-primary fs-3 mb-2"></i>
                                <h6 class="text-primary mb-1">Responsive</h6>
                                <small class="text-muted">Mobile optimized</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 bg-warning bg-opacity-10">
                            <div class="card-body text-center py-3">
                                <i class="bi bi-palette text-warning fs-3 mb-2"></i>
                                <h6 class="text-warning mb-1">Customizable</h6>
                                <small class="text-muted">Brand colors</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Loading Spinners -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-arrow-repeat text-primary me-3 fs-3"></i>
                    <h2 class="text-primary mb-0">Loading Spinners</h2>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-gear text-muted me-2"></i>
                            <h5 class="mb-0">Spinner Variants</h5>
                        </div>
                        <p class="text-muted mb-0 mt-1">Professional loading indicators with Bootstrap 5 styling</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Border Spinners -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-circle text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Border Spinners</h6>
                                </div>
                                <div class="loading-demo-grid">
                                    <div class="loading-demo-item">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Small</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Default</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-border spinner-lg text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Large</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-border spinner-xl text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Extra Large</small>
                                    </div>
                                </div>
                                
                                <div class="loading-demo-colors mt-3">
                                    <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-border text-secondary" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-border text-success" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-border text-warning" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-border text-danger" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                                </div>
                            </div>
                            
                            <!-- Growing Spinners -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-record-circle text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Growing Spinners</h6>
                                </div>
                                <div class="loading-demo-grid">
                                    <div class="loading-demo-item">
                                        <div class="spinner-grow spinner-grow-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Small</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-grow text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Default</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-grow spinner-lg text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Large</small>
                                    </div>
                                    <div class="loading-demo-item">
                                        <div class="spinner-grow spinner-xl text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small class="text-muted">Extra Large</small>
                                    </div>
                                </div>
                                
                                <div class="loading-demo-colors mt-3">
                                    <div class="spinner-grow text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-grow text-secondary" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-grow text-success" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-grow text-warning" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-grow text-danger" role="status"><span class="visually-hidden">Loading...</span></div>
                                    <div class="spinner-grow text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                                </div>
                            </div>
                            
                            <!-- Button Loading States -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-play-btn text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Button Loading States</h6>
                                </div>
                                <div class="d-flex flex-wrap gap-3">
                                    <button class="btn btn-primary position-relative" type="button" disabled>
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Processing...</span>
                                    </button>
                                    <button class="btn btn-success position-relative" type="button" disabled>
                                        <span class="spinner-grow spinner-grow-sm me-2" role="status"></span>
                                        <span>Saving...</span>
                                    </button>
                                    <button class="btn btn-warning position-relative" type="button" disabled>
                                        <i class="bi bi-arrow-clockwise spinner-icon me-2"></i>
                                        <span>Updating...</span>
                                    </button>
                                    <button class="btn btn-outline-primary position-relative" type="button" disabled>
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Submitting...</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Icon-Based Spinners -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-stars text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Icon-Based Spinners</h6>
                                </div>
                                <div class="d-flex align-items-center gap-4">
                                    <div class="text-center">
                                        <i class="bi bi-arrow-clockwise text-primary spinner-icon fs-4"></i>
                                        <div><small class="text-muted">Refresh</small></div>
                                    </div>
                                    <div class="text-center">
                                        <i class="bi bi-arrow-repeat text-success spinner-icon fs-4"></i>
                                        <div><small class="text-muted">Sync</small></div>
                                    </div>
                                    <div class="text-center">
                                        <i class="bi bi-gear text-warning spinner-icon fs-4"></i>
                                        <div><small class="text-muted">Settings</small></div>
                                    </div>
                                    <div class="text-center">
                                        <i class="bi bi-hourglass text-primary spinner-icon fs-4"></i>
                                        <div><small class="text-muted">Waiting</small></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Progress Indicators -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-bar-chart text-primary me-3 fs-3"></i>
                    <h2 class="text-primary mb-0">Progress Indicators</h2>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-speedometer text-muted me-2"></i>
                            <h5 class="mb-0">Progress Tracking</h5>
                        </div>
                        <p class="text-muted mb-0 mt-1">Professional progress bars and indicators</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Linear Progress -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-graph-up text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Linear Progress</h6>
                                </div>
                                
                                <div class="progress-demo mb-4">
                                    <label class="form-label d-flex align-items-center">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        <span>Upload Progress</span>
                                        <span class="ms-auto text-primary fw-semibold">75%</span>
                                    </label>
                                    <div class="progress progress-enhanced">
                                        <div class="progress-bar bg-gradient" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="progress-demo mb-4">
                                    <label class="form-label d-flex align-items-center">
                                        <i class="bi bi-cloud-upload text-primary me-2"></i>
                                        <span>Processing</span>
                                        <span class="ms-auto text-primary fw-semibold">45%</span>
                                    </label>
                                    <div class="progress progress-enhanced">
                                        <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated" role="progressbar" style="width: 45%" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                
                                <div class="progress-demo">
                                    <label class="form-label d-flex align-items-center">
                                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                                        <span>Storage Usage</span>
                                        <span class="ms-auto text-warning fw-semibold">90%</span>
                                    </label>
                                    <div class="progress progress-enhanced">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 90%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Circular Progress -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-pie-chart text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Circular Progress</h6>
                                </div>
                                
                                <div class="d-flex flex-wrap gap-4 justify-content-center">
                                    <div class="circular-progress-container">
                                        <div class="circular-progress-circle" data-progress="25">
                                            <i class="bi bi-download text-primary"></i>
                                            <span class="circular-progress-text">25%</span>
                                        </div>
                                        <label class="circular-progress-label">Download</label>
                                    </div>
                                    
                                    <div class="circular-progress-container">
                                        <div class="circular-progress-circle circular-progress-md" data-progress="60">
                                            <i class="bi bi-cloud-upload text-success"></i>
                                            <span class="circular-progress-text">60%</span>
                                        </div>
                                        <label class="circular-progress-label">Upload</label>
                                    </div>
                                    
                                    <div class="circular-progress-container">
                                        <div class="circular-progress-circle circular-progress-lg" data-progress="85">
                                            <i class="bi bi-cpu text-warning"></i>
                                            <span class="circular-progress-text">85%</span>
                                        </div>
                                        <label class="circular-progress-label">Processing</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Step Progress -->
                            <div class="col-12 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-list-ol text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Step Progress Indicator</h6>
                                </div>
                                
                                <div class="step-progress-wrapper">
                                    <div class="step-progress">
                                        <div class="step-progress-item completed">
                                            <div class="step-progress-number">
                                                <i class="bi bi-check"></i>
                                            </div>
                                            <div class="step-progress-content">
                                                <h6>Account Setup</h6>
                                                <p>Create your profile</p>
                                            </div>
                                        </div>
                                        <div class="step-progress-item active">
                                            <div class="step-progress-number">2</div>
                                            <div class="step-progress-content">
                                                <h6>Venue Selection</h6>
                                                <p>Choose your venue</p>
                                            </div>
                                        </div>
                                        <div class="step-progress-item">
                                            <div class="step-progress-number">3</div>
                                            <div class="step-progress-content">
                                                <h6>Payment</h6>
                                                <p>Complete booking</p>
                                            </div>
                                        </div>
                                        <div class="step-progress-item">
                                            <div class="step-progress-number">4</div>
                                            <div class="step-progress-content">
                                                <h6>Confirmation</h6>
                                                <p>Booking confirmed</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Skeleton Loaders -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-grid text-primary me-3 fs-3"></i>
                    <h2 class="text-primary mb-0">Skeleton Loaders</h2>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-layout-text-window text-muted me-2"></i>
                            <h5 class="mb-0">Content Placeholders</h5>
                        </div>
                        <p class="text-muted mb-0 mt-1">Elegant skeleton loaders for better perceived performance</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Card Skeleton -->
                            <div class="col-md-4 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-card-text text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Card Skeleton</h6>
                                </div>
                                <div class="skeleton-card-modern">
                                    <div class="skeleton-image-modern"></div>
                                    <div class="skeleton-content-modern">
                                        <div class="skeleton-title-modern"></div>
                                        <div class="skeleton-text-modern"></div>
                                        <div class="skeleton-text-modern"></div>
                                        <div class="skeleton-text-short-modern"></div>
                                        <div class="d-flex gap-2 mt-3">
                                            <div class="skeleton-button-modern"></div>
                                            <div class="skeleton-button-outline-modern"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- List Skeleton -->
                            <div class="col-md-4 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-list-ul text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">List Skeleton</h6>
                                </div>
                                <div class="skeleton-list-modern">
                                    <div class="skeleton-list-item-modern">
                                        <div class="skeleton-avatar-modern"></div>
                                        <div class="skeleton-list-content-modern">
                                            <div class="skeleton-title-modern"></div>
                                            <div class="skeleton-text-modern"></div>
                                        </div>
                                        <div class="skeleton-icon-modern"></div>
                                    </div>
                                    <div class="skeleton-list-item-modern">
                                        <div class="skeleton-avatar-modern"></div>
                                        <div class="skeleton-list-content-modern">
                                            <div class="skeleton-title-modern"></div>
                                            <div class="skeleton-text-modern"></div>
                                        </div>
                                        <div class="skeleton-icon-modern"></div>
                                    </div>
                                    <div class="skeleton-list-item-modern">
                                        <div class="skeleton-avatar-modern"></div>
                                        <div class="skeleton-list-content-modern">
                                            <div class="skeleton-title-modern"></div>
                                            <div class="skeleton-text-modern"></div>
                                        </div>
                                        <div class="skeleton-icon-modern"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Skeleton -->
                            <div class="col-md-4 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-form text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Form Skeleton</h6>
                                </div>
                                <div class="skeleton-form-modern">
                                    <div class="skeleton-form-group-modern">
                                        <div class="skeleton-label-modern"></div>
                                        <div class="skeleton-input-modern"></div>
                                    </div>
                                    <div class="skeleton-form-group-modern">
                                        <div class="skeleton-label-modern"></div>
                                        <div class="skeleton-input-modern"></div>
                                    </div>
                                    <div class="skeleton-form-group-modern">
                                        <div class="skeleton-label-modern"></div>
                                        <div class="skeleton-textarea-modern"></div>
                                    </div>
                                    <div class="d-flex gap-3 mt-4">
                                        <div class="skeleton-button-modern flex-grow-1"></div>
                                        <div class="skeleton-button-outline-modern"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Loading States -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-layers text-primary me-3 fs-3"></i>
                    <h2 class="text-primary mb-0">Loading States</h2>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-window text-muted me-2"></i>
                            <h5 class="mb-0">Application States</h5>
                        </div>
                        <p class="text-muted mb-0 mt-1">Professional loading states for different contexts</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Full Page Loading -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-window-fullscreen text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Full Page Loading</h6>
                                </div>
                                <div class="loading-state-demo">
                                    <div class="page-loading-modern">
                                        <div class="page-loading-content">
                                            <div class="spinner-border text-primary mb-3" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <h6 class="text-primary">Loading Dashboard</h6>
                                            <p class="text-muted mb-0">Please wait while we prepare your data...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Content Loading -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-file-earmark text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Content Loading</h6>
                                </div>
                                <div class="loading-state-demo">
                                    <div class="content-loading-modern">
                                        <div class="content-loading-overlay">
                                            <div class="spinner-border text-primary mb-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <small class="text-muted">Loading content...</small>
                                        </div>
                                        <div class="content-placeholder">
                                            <p class="mb-2">Sample content will appear here</p>
                                            <p class="mb-0">Loading overlay will disappear when ready</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Search Loading -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-search text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Search Loading</h6>
                                </div>
                                <div class="search-loading-demo">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" placeholder="Search venues..." value="wedding venues">
                                        <button class="btn btn-primary" type="button" disabled>
                                            <span class="spinner-border spinner-border-sm" role="status"></span>
                                        </button>
                                    </div>
                                    <div class="search-results-loading mt-3">
                                        <div class="skeleton-list-modern">
                                            <div class="skeleton-list-item-modern">
                                                <div class="skeleton-avatar-modern"></div>
                                                <div class="skeleton-list-content-modern">
                                                    <div class="skeleton-title-modern"></div>
                                                    <div class="skeleton-text-modern"></div>
                                                </div>
                                            </div>
                                            <div class="skeleton-list-item-modern">
                                                <div class="skeleton-avatar-modern"></div>
                                                <div class="skeleton-list-content-modern">
                                                    <div class="skeleton-title-modern"></div>
                                                    <div class="skeleton-text-modern"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- File Upload -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-cloud-upload text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">File Upload</h6>
                                </div>
                                <div class="upload-demo">
                                    <div class="upload-area-modern">
                                        <div class="upload-area-content">
                                            <i class="bi bi-cloud-upload fs-1 text-primary mb-3"></i>
                                            <h6 class="mb-2">Drop files here or click to upload</h6>
                                            <p class="text-muted mb-0">Maximum file size: 10MB</p>
                                        </div>
                                    </div>
                                    <div class="upload-progress-modern mt-3" id="demoUploadProgress" style="display: none;">
                                        <div class="upload-progress-header">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark-image text-primary me-2"></i>
                                                <span class="upload-filename">wedding-photo.jpg</span>
                                            </div>
                                            <span class="upload-percentage text-primary fw-semibold">75%</span>
                                        </div>
                                        <div class="progress progress-enhanced mt-2">
                                            <div class="progress-bar bg-gradient" role="progressbar" style="width: 75%"></div>
                                        </div>
                                        <div class="upload-progress-footer mt-2">
                                            <small class="text-muted">2.1 MB of 2.8 MB</small>
                                            <small class="text-muted">30 seconds remaining</small>
                                        </div>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="toggleUploadDemo()">
                                        <i class="bi bi-play me-1"></i>
                                        Demo Upload
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Interactive Examples -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex align-items-center mb-4">
                    <i class="bi bi-mouse text-primary me-3 fs-3"></i>
                    <h2 class="text-primary mb-0">Interactive Examples</h2>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-play-circle text-muted me-2"></i>
                            <h5 class="mb-0">Try It Yourself</h5>
                        </div>
                        <p class="text-muted mb-0 mt-1">Interactive demonstrations of loading components</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Form Submission Demo -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-send text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Form Submission</h6>
                                </div>
                                <form class="demo-form" onsubmit="handleFormSubmit(event)">
                                    <div class="mb-3">
                                        <label for="eventName" class="form-label d-flex align-items-center">
                                            <i class="bi bi-calendar-event me-2"></i>
                                            Event Name
                                        </label>
                                        <input type="text" class="form-control" id="eventName" value="Summer Wedding Reception" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="eventDate" class="form-label d-flex align-items-center">
                                            <i class="bi bi-calendar3 me-2"></i>
                                            Event Date
                                        </label>
                                        <input type="date" class="form-control" id="eventDate" value="2024-06-15" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary position-relative" id="submitBtn">
                                        <span id="submitIcon" class="me-2">
                                            <i class="bi bi-check-circle"></i>
                                        </span>
                                        <span id="submitText">Save Event</span>
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Data Loading Demo -->
                            <div class="col-md-6 mb-4">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-database text-primary me-2"></i>
                                    <h6 class="text-primary mb-0">Data Loading</h6>
                                </div>
                                <div id="dataContainer" class="data-loading-demo">
                                    <div class="text-center py-4">
                                        <div class="spinner-border text-primary mb-3" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <h6 class="text-primary">Loading Venue Data</h6>
                                        <p class="text-muted mb-0">Fetching available venues...</p>
                                    </div>
                                </div>
                                <button class="btn btn-outline-secondary btn-sm mt-3" onclick="toggleDataDemo()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Toggle Loading
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Professional Loading Component JavaScript -->
    <script>
        // Initialize circular progress indicators
        document.addEventListener('DOMContentLoaded', function() {
            const circles = document.querySelectorAll('.circular-progress-circle');
            circles.forEach(circle => {
                const progress = circle.getAttribute('data-progress');
                circle.style.setProperty('--progress', progress);
            });
        });
        
        // Form submission demo
        function handleFormSubmit(event) {
            event.preventDefault();
            const btn = document.getElementById('submitBtn');
            const icon = document.getElementById('submitIcon');
            const text = document.getElementById('submitText');
            
            // Loading state
            btn.disabled = true;
            btn.classList.add('btn-loading');
            icon.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
            text.textContent = 'Saving...';
            
            // Simulate API call
            setTimeout(() => {
                // Success state
                icon.innerHTML = '<i class="bi bi-check-circle"></i>';
                text.textContent = 'Saved Successfully!';
                btn.classList.remove('btn-loading');
                btn.classList.add('btn-success');
                
                // Reset after delay
                setTimeout(() => {
                    btn.disabled = false;
                    btn.classList.remove('btn-success');
                    text.textContent = 'Save Event';
                }, 1500);
            }, 2000);
        }
        
        // Data loading demo
        let dataLoaded = false;
        function toggleDataDemo() {
            const container = document.getElementById('dataContainer');
            
            if (dataLoaded) {
                // Show loading state
                container.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h6 class="text-primary">Loading Venue Data</h6>
                        <p class="text-muted mb-0">Fetching available venues...</p>
                    </div>
                `;
                dataLoaded = false;
            } else {
                // Show loaded data
                setTimeout(() => {
                    container.innerHTML = `
                        <div class="card border-success">
                            <div class="card-header bg-success bg-opacity-10">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    <h6 class="mb-0 text-success">Venues Loaded Successfully</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-building text-primary me-2"></i>
                                            <span>Grand Ballroom</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-flower1 text-success me-2"></i>
                                            <span>Garden Terrace</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-people text-primary me-2"></i>
                                            <span>Conference Hall</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    dataLoaded = true;
                }, 1500);
            }
        }
        
        // Upload demo
        function toggleUploadDemo() {
            const progress = document.getElementById('demoUploadProgress');
            const progressBar = progress.querySelector('.progress-bar');
            const percentage = progress.querySelector('.upload-percentage');
            
            progress.style.display = 'block';
            
            let currentProgress = 0;
            const interval = setInterval(() => {
                currentProgress += Math.random() * 10;
                if (currentProgress >= 100) {
                    currentProgress = 100;
                    clearInterval(interval);
                    percentage.textContent = 'Complete';
                    percentage.classList.add('text-success');
                    progressBar.classList.add('bg-success');
                    
                    // Hide after success
                    setTimeout(() => {
                        progress.style.display = 'none';
                        percentage.classList.remove('text-success');
                        progressBar.classList.remove('bg-success');
                        percentage.textContent = '0%';
                        currentProgress = 0;
                    }, 2000);
                } else {
                    percentage.textContent = Math.round(currentProgress) + '%';
                }
                progressBar.style.width = currentProgress + '%';
                progressBar.setAttribute('aria-valuenow', currentProgress);
            }, 200);
        }
    </script>
</body>
</html> 