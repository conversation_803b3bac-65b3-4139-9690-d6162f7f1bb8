<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Navbar Component - CozyWish Design System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #F8F9FA;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <nav class="guest-navbar fixed-top">
        <div class="container">
            <div class="row align-items-center">
                <!-- Brand Logo -->
                <div class="col-auto">
                    <div class="guest-navbar-brand">
                        <a href="#" class="guest-navbar-logo">
                            <img src="../logo/logo-light.svg" alt="CozyWish Logo">
                        </a>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-auto ms-auto">
                    <div class="guest-navbar-actions">
                        <!-- For Business Button -->
                        <a href="#" class="btn-for-business me-3">
                            <i class="bi bi-building"></i>
                            <span>For Business</span>
                        </a>

                        <!-- Menu Dropdown -->
                        <div class="dropdown">
                            <button class="btn-menu dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person"></i>
                                <span>Menu</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Log in
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-person-plus me-2"></i>
                                    Sign Up
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-building-gear me-2"></i>
                                    Business Login
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Test Content to demonstrate fixed navbar -->
    <div style="padding-top: 80px;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="mt-5 mb-4">Welcome to CozyWish</h1>
                    <p class="lead mb-4">This is test content to demonstrate the fixed navbar functionality. Scroll down to see the navbar stay at the top.</p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Section 1</h5>
                                    <p class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Section 2</h5>
                                    <p class="card-text">Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Feature 1</h5>
                                    <p class="card-text">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Feature 2</h5>
                                    <p class="card-text">Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Feature 3</h5>
                                    <p class="card-text">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Long Content Section</h5>
                                    <p class="card-text">This section contains more content to ensure there's enough scrolling to test the fixed navbar. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                                    <p class="card-text">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                                    <p class="card-text">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">About CozyWish</h5>
                                    <p class="card-text">CozyWish is a platform that helps you find and book the perfect venues for your events. Whether you're planning a wedding, corporate event, or birthday party, we have the perfect space for you.</p>
                                    <p class="card-text">Our platform connects venue owners with event planners, making it easy to find the right space at the right price. With our comprehensive search tools and detailed venue information, you can make informed decisions about your event space.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Contact Us</h5>
                                    <p class="card-text">Get in touch with our team for any questions or support you might need.</p>
                                    <button class="btn btn-primary">Contact Support</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-5">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Footer Content</h5>
                                    <p class="card-text">This is the bottom of the page content. The navbar should remain fixed at the top as you scroll through all this content.</p>
                                    <p class="card-text">You can now test the fixed navbar functionality by scrolling up and down this page. The navbar will stay visible at the top of the viewport at all times.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 