<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Navbar Component - CozyWish Design System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #F8F9FA;
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <nav class="guest-navbar fixed-top">
        <div class="container">
            <div class="row align-items-center">
                <!-- Brand Logo -->
                <div class="col-auto">
                    <div class="guest-navbar-brand">
                        <a href="#" class="guest-navbar-logo">
                            <img src="../logo/logo-light.svg" alt="CozyWish Logo">
                        </a>
                    </div>
                </div>

                <!-- Customer Actions -->
                <div class="col-auto ms-auto">
                    <div class="guest-navbar-actions">
                        <!-- Shopping Cart with Badge -->
                        <div class="position-relative me-3">
                            <a href="#" class="btn-customer-action">
                                <i class="bi bi-cart"></i>
                                <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill">
                                    3
                                    <span class="visually-hidden">items in cart</span>
                                </span>
                            </a>
                        </div>

                        <!-- Notifications with Badge -->
                        <div class="position-relative me-3">
                            <a href="#" class="btn-customer-action">
                                <i class="bi bi-bell"></i>
                                <span class="badge bg-warning position-absolute top-0 start-100 translate-middle rounded-pill">
                                    5
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                        </div>

                        <!-- User Menu Dropdown -->
                        <div class="dropdown">
                            <button class="btn-customer-menu dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <span>Menu</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-person me-2"></i>
                                    My Profile
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-speedometer2 me-2"></i>
                                    Dashboard
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-calendar-check me-2"></i>
                                    My Bookings
                                    <span class="badge bg-primary ms-auto">2</span>
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-cart me-2"></i>
                                    My Cart
                                    <span class="badge bg-danger ms-auto">3</span>
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-star me-2"></i>
                                    My Reviews
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-bell me-2"></i>
                                    Notifications
                                    <span class="badge bg-warning ms-auto">5</span>
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-credit-card me-2"></i>
                                    Payment History
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#">
                                    <i class="bi bi-box-arrow-right me-2"></i>
                                    Log out
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Test Content to demonstrate fixed navbar -->
    <div style="padding-top: 80px;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="mt-5 mb-4">Welcome back! 👋</h1>
                    <p class="lead mb-4">This is the customer navbar for logged-in users. It includes shopping cart, notifications, and a comprehensive user menu.</p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Dashboard Overview</h5>
                                    <p class="card-text">Access your personalized dashboard to view your bookings, cart items, and recent activity.</p>
                                    <button class="btn btn-primary">Go to Dashboard</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Recent Bookings</h5>
                                    <p class="card-text">You have 2 upcoming bookings. Click to manage your reservations and view details.</p>
                                    <button class="btn btn-outline-primary">View Bookings</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Shopping Cart</h5>
                                    <p class="card-text">You have 3 items in your cart ready for checkout.</p>
                                    <button class="btn btn-success">Checkout Now</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Notifications</h5>
                                    <p class="card-text">You have 5 unread notifications waiting for your attention.</p>
                                    <button class="btn btn-warning">View All</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Your Reviews</h5>
                                    <p class="card-text">Share your experience and read reviews from other customers.</p>
                                    <button class="btn btn-outline-primary">Manage Reviews</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Featured Venues</h5>
                                    <p class="card-text">Discover new venues perfect for your next event. Our curated selection includes the most popular and highly-rated venues in your area.</p>
                                    <p class="card-text">Browse through wedding venues, corporate spaces, birthday party locations, and more. Each venue comes with detailed photos, reviews, and pricing information.</p>
                                    <button class="btn btn-primary">Explore Venues</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Quick Actions</h5>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-outline-primary btn-sm">Book a Venue</button>
                                        <button class="btn btn-outline-secondary btn-sm">View Favorites</button>
                                        <button class="btn btn-outline-success btn-sm">Track Orders</button>
                                        <button class="btn btn-outline-info btn-sm">Get Support</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Account Summary</h5>
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <h3 class="text-primary">2</h3>
                                            <p class="text-muted">Active Bookings</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h3 class="text-success">8</h3>
                                            <p class="text-muted">Completed Events</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h3 class="text-warning">5</h3>
                                            <p class="text-muted">Pending Reviews</p>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <h3 class="text-info">$2,450</h3>
                                            <p class="text-muted">Total Spent</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-5">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Customer Navbar Features</h5>
                                    <p class="card-text">This customer navbar includes all the essential features for logged-in users:</p>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">🛒 <strong>Shopping Cart</strong> - With item count badge (3 items)</li>
                                        <li class="mb-2">🔔 <strong>Notifications</strong> - With unread count badge (5 notifications)</li>
                                        <li class="mb-2">👤 <strong>User Menu</strong> - Comprehensive dropdown with all customer options</li>
                                        <li class="mb-2">📊 <strong>Dashboard Access</strong> - Quick link to customer dashboard</li>
                                        <li class="mb-2">📅 <strong>Bookings Management</strong> - View and manage reservations</li>
                                        <li class="mb-2">⭐ <strong>Reviews System</strong> - Manage customer reviews</li>
                                        <li class="mb-2">💳 <strong>Payment History</strong> - Access to transaction records</li>
                                        <li class="mb-2">🚪 <strong>Secure Logout</strong> - Safe account logout option</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 