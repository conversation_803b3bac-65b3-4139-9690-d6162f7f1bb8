<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CozyWish Design System - Typography</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>

<body class="bg-page">
    <div class="container py-5">
        <div class="row mb-5">
            <div class="col-12">
                <h1 class="text-primary mb-3">CozyWish Typography System</h1>
                <p class="text-body lead">Clean, bold, and readable typography using standard fonts that are easy to read and widely used across the web.</p>
            </div>
        </div>
        
        <!-- Headings -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4">Headings</h2>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Heading Levels</h5>
                    </div>
                    <div class="card-body">
                        <h1 class="text-primary">Heading 1 - Main Page Title (Bold & Clean)</h1>
                        <h2 class="text-primary">Heading 2 - Section Title (Bold & Readable)</h2>
                        <h3 class="text-primary">Heading 3 - Subsection Title (Professional)</h3>
                        <h4 class="text-primary">Heading 4 - Card Title (Clear & Bold)</h4>
                        <h5 class="text-primary">Heading 5 - Small Title (Easy to Read)</h5>
                        <h6 class="text-primary">Heading 6 - Caption Title (Standard Size)</h6>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Body Text -->
        <div class="row mb-5">
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Body Text</h3>
                <div class="card">
                    <div class="card-body">
                        <p class="text-body">This is a regular paragraph with bold, readable text. It uses standard fonts like Segoe UI and Roboto that are commonly used across the web for excellent readability and professional appearance.</p>
                        
                        <p class="text-body">This is another paragraph showing how the bold typography system works. The text is clean, easy to read, and uses font weights that make content stand out clearly without being fancy or difficult to read.</p>
                        
                        <p class="lead text-body">This is a lead paragraph that's slightly larger and bolder than regular text. It's perfect for introducing sections or highlighting important information with clear, professional typography.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Text Utilities</h3>
                <div class="card">
                    <div class="card-body">
                        <p class="text-primary">                                        <strong>Primary Text</strong> - Bold text for headings and important elements</p>
                        <p class="text-secondary"><strong>Secondary Text</strong> - Bold supporting information</p>
                        <p class="text-muted"><strong>Muted Text</strong> - Less important but still readable</p>
                        <p class="text-success"><strong>Success Text</strong> - Bold positive feedback</p>
                        <p class="text-danger"><strong>Danger Text</strong> - Bold error messages</p>
                        <p class="text-warning"><strong>Warning Text</strong> - Bold cautionary information</p>
                        <p class="text-info"><strong>Info Text</strong> - Bold informational content</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Links -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Links</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Standard Links</h5>
                                <p class="text-body">This is a paragraph with a <a href="#" class="text-decoration-none">bold link</a> that uses our brand colors. All links are bold by default for clear visibility and easy reading.</p>
                                
                                <p class="text-body">Here's another example with a <a href="#" class="text-decoration-underline">link with underline</a> for additional emphasis when needed. The bold typography makes links stand out clearly.</p>
                                
                                <p class="text-body">You can also have <a href="#" class="fw-bold">extra bold links</a> for maximum emphasis on important navigation elements.</p>
                            </div>
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Link States</h5>
                                <p class="text-body">Normal link: <a href="#" class="text-decoration-none">Click here</a></p>
                                <p class="text-body">Hover state: <a href="#" class="text-decoration-none" style="color: #5A342A;">Hovered link</a></p>
                                <p class="text-body">Active state: <a href="#" class="text-decoration-none" style="color: #43251B; font-weight: bold;">Active link</a></p>
                                <p class="text-body">Visited state: <a href="#" class="text-decoration-none" style="color: #8B7355;">Visited link</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Lists -->
        <div class="row mb-5">
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Unordered Lists</h3>
                <div class="card">
                    <div class="card-body">
                        <ul class="text-body">
                            <li>First item in an unordered list</li>
                            <li>Second item with some additional text to show how longer content flows</li>
                            <li>Third item in the list</li>
                            <li>Fourth item demonstrating list styling</li>
                        </ul>
                        
                        <ul class="list-unstyled text-body">
                            <li>✓ Unstyled list item with checkmark</li>
                            <li>✓ Another unstyled item</li>
                            <li>✓ Perfect for feature lists</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Ordered Lists</h3>
                <div class="card">
                    <div class="card-body">
                        <ol class="text-body">
                            <li>First step in a process</li>
                            <li>Second step with additional details about what needs to be done</li>
                            <li>Third step in the sequence</li>
                            <li>Final step to complete the process</li>
                        </ol>
                        
                        <ol class="list-group list-group-numbered">
                            <li class="list-group-item border-0 bg-transparent ps-0">Numbered list group item</li>
                            <li class="list-group-item border-0 bg-transparent ps-0">Another numbered item</li>
                            <li class="list-group-item border-0 bg-transparent ps-0">Third numbered item</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Text Alignment & Transformation -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Text Alignment & Transformation</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h5 class="text-primary mb-3">Alignment</h5>
                                <p class="text-start text-body">Left aligned text (default)</p>
                                <p class="text-center text-body">Center aligned text</p>
                                <p class="text-end text-body">Right aligned text</p>
                                <p class="text-justify text-body">Justified text that spreads across the full width of the container for a clean, professional appearance.</p>
                            </div>
                            <div class="col-md-4">
                                <h5 class="text-primary mb-3">Transformation</h5>
                                <p class="text-lowercase text-body">LOWERCASE TEXT</p>
                                <p class="text-uppercase text-body">uppercase text</p>
                                <p class="text-capitalize text-body">capitalized text</p>
                                <p class="fw-bold text-body">Bold text</p>
                                <p class="fst-italic text-body">Italic text</p>
                            </div>
                            <div class="col-md-4">
                                <h5 class="text-primary mb-3">Font Weights</h5>
                                <p class="fw-light text-body">Light weight text</p>
                                <p class="fw-normal text-body">Normal weight text</p>
                                <p class="fw-bold text-body">Bold weight text</p>
                                <p class="fw-bolder text-body">Bolder weight text</p>
                                <p class="fw-lighter text-body">Lighter weight text</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Blockquotes -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Blockquotes</h3>
                <div class="card">
                    <div class="card-body">
                        <blockquote class="blockquote">
                            <p class="text-body">This is a standard blockquote that can be used for testimonials, quotes, or highlighted content. It maintains our brand typography while providing visual emphasis.</p>
                            <footer class="blockquote-footer text-muted">Someone famous in <cite title="Source Title">Source Title</cite></footer>
                        </blockquote>
                        
                        <blockquote class="blockquote text-center">
                            <p class="text-body">This is a centered blockquote that works well for featured testimonials or important quotes that need to stand out on the page.</p>
                            <footer class="blockquote-footer text-muted">Another source</footer>
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Code & Preformatted Text -->
        <div class="row mb-5">
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Inline Code</h3>
                <div class="card">
                    <div class="card-body">
                        <p class="text-body">You can use <code>&lt;code&gt;</code> tags for inline code snippets like <code>var example = "hello world";</code> within your text content.</p>
                        
                        <p class="text-body">For keyboard shortcuts, use <kbd>Ctrl</kbd> + <kbd>C</kbd> to copy and <kbd>Ctrl</kbd> + <kbd>V</kbd> to paste.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3 class="text-primary mb-3">Code Blocks</h3>
                <div class="card">
                    <div class="card-body">
                        <pre class="text-body"><code>&lt;div class="container"&gt;
  &lt;h1 class="text-primary"&gt;Hello World&lt;/h1&gt;
  &lt;p class="text-body"&gt;This is a code block example.&lt;/p&gt;
&lt;/div&gt;</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Text Sizes -->
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="text-primary mb-3">Text Sizes</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <p class="fs-1 text-body">fs-1 (2.5rem)</p>
                                <p class="fs-2 text-body">fs-2 (2rem)</p>
                                <p class="fs-3 text-body">fs-3 (1.75rem)</p>
                            </div>
                            <div class="col-md-3">
                                <p class="fs-4 text-body">fs-4 (1.5rem)</p>
                                <p class="fs-5 text-body">fs-5 (1.25rem)</p>
                                <p class="fs-6 text-body">fs-6 (1rem)</p>
                            </div>
                            <div class="col-md-3">
                                <p class="text-body">Normal text (1rem)</p>
                                <p class="small text-body">Small text (0.875rem)</p>
                                <p class="text-body" style="font-size: 0.75rem;">Extra small text (0.75rem)</p>
                            </div>
                            <div class="col-md-3">
                                <p class="display-1 text-primary">Display 1</p>
                                <p class="display-6 text-primary">Display 6</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Color Palette Reference -->
        <div class="row">
            <div class="col-12">
                <h3 class="text-primary mb-3">Typography Color Palette</h3>
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded me-3" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Primary Text</strong><br>
                                        <small class="text-muted">#43251B - Bold headings & links</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-secondary rounded me-3" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Secondary Text</strong><br>
                                        <small class="text-muted">#5A342A - Bold supporting text</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-dark rounded me-3" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Body Text</strong><br>
                                        <small class="text-muted">#222222 - Clean & readable</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded me-3 border" style="width: 30px; height: 30px;"></div>
                                    <div>
                                        <strong>Background</strong><br>
                                        <small class="text-muted">#F8F9FA - Clean page background</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 