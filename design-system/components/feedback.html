<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Feedback Components - CozyWish Store</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Bootstrap Icons CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-primary mb-4 d-flex align-items-center">
                    <i class="bi bi-star-fill me-3"></i>
                    Product Feedback System
                </h1>
                <p class="lead text-muted mb-5">
                    Professional product rating and review components for CozyWish store
                </p>
            </div>
        </div>

        <!-- Product Rating Display Section -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4 d-flex align-items-center">
                    <i class="bi bi-stars me-2"></i>
                    Product Rating Display
                </h2>
                
                <!-- Product Rating Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">Product Rating Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="rating-summary text-center">
                                    <div class="average-rating">4.5</div>
                                    <div class="star-display mb-2">
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-half text-warning"></i>
                                    </div>
                                    <div class="review-count">Based on 247 reviews</div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="rating-breakdown">
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="rating-label">5</span>
                                        <i class="bi bi-star-fill text-warning mx-2"></i>
                                        <div class="progress flex-grow-1 me-3">
                                            <div class="progress-bar" style="width: 68%"></div>
                                        </div>
                                        <span class="rating-percentage">68%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="rating-label">4</span>
                                        <i class="bi bi-star-fill text-warning mx-2"></i>
                                        <div class="progress flex-grow-1 me-3">
                                            <div class="progress-bar" style="width: 22%"></div>
                                        </div>
                                        <span class="rating-percentage">22%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="rating-label">3</span>
                                        <i class="bi bi-star-fill text-warning mx-2"></i>
                                        <div class="progress flex-grow-1 me-3">
                                            <div class="progress-bar" style="width: 7%"></div>
                                        </div>
                                        <span class="rating-percentage">7%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="rating-label">2</span>
                                        <i class="bi bi-star-fill text-warning mx-2"></i>
                                        <div class="progress flex-grow-1 me-3">
                                            <div class="progress-bar" style="width: 2%"></div>
                                        </div>
                                        <span class="rating-percentage">2%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center">
                                        <span class="rating-label">1</span>
                                        <i class="bi bi-star-fill text-warning mx-2"></i>
                                        <div class="progress flex-grow-1 me-3">
                                            <div class="progress-bar" style="width: 1%"></div>
                                        </div>
                                        <span class="rating-percentage">1%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Interactive Product Rating -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">Interactive Product Rating</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>Rate This Product</h4>
                                <div class="product-rating-input" data-rating="0">
                                    <i class="bi bi-star rating-star" data-value="1"></i>
                                    <i class="bi bi-star rating-star" data-value="2"></i>
                                    <i class="bi bi-star rating-star" data-value="3"></i>
                                    <i class="bi bi-star rating-star" data-value="4"></i>
                                    <i class="bi bi-star rating-star" data-value="5"></i>
                                    <span class="rating-text ms-2">Click to rate</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h4>Product Rating Display</h4>
                                <div class="product-rating-display">
                                    <div class="rating-stars">
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <i class="bi bi-star text-muted"></i>
                                    </div>
                                    <span class="rating-value ms-2">4.0 out of 5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Reviews Section -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4 d-flex align-items-center">
                    <i class="bi bi-chat-square-text me-2"></i>
                    Customer Reviews
                </h2>
                
                <!-- Review Filters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="card-title">Review Filters</h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label for="ratingFilter" class="form-label">Filter by Rating</label>
                                <select class="form-select" id="ratingFilter">
                                    <option value="all">All Ratings</option>
                                    <option value="5">5 Stars</option>
                                    <option value="4">4 Stars</option>
                                    <option value="3">3 Stars</option>
                                    <option value="2">2 Stars</option>
                                    <option value="1">1 Star</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sortReviews" class="form-label">Sort By</label>
                                <select class="form-select" id="sortReviews">
                                    <option value="newest">Newest First</option>
                                    <option value="oldest">Oldest First</option>
                                    <option value="highest">Highest Rated</option>
                                    <option value="lowest">Lowest Rated</option>
                                    <option value="helpful">Most Helpful</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="searchReviews" class="form-label">Search Reviews</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchReviews" placeholder="Search reviews...">
                                    <button class="btn btn-outline-primary" type="button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100">Apply Filters</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Review Cards -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">Customer Reviews</h3>
                        <button class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Write Review
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Review Item 1 -->
                        <div class="review-item">
                            <div class="review-header d-flex justify-content-between align-items-start">
                                <div class="reviewer-info d-flex align-items-center">
                                    <div class="reviewer-avatar">
                                        <i class="bi bi-person-circle text-primary"></i>
                                    </div>
                                    <div class="reviewer-details ms-3">
                                        <h5 class="reviewer-name mb-1">Sarah Johnson</h5>
                                        <div class="review-meta d-flex align-items-center">
                                            <div class="review-rating me-2">
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                            </div>
                                            <span class="review-date text-muted">December 15, 2024</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="review-actions">
                                    <button class="btn btn-link btn-sm text-muted">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="review-content mt-3">
                                <h6 class="review-title">Excellent Quality Product!</h6>
                                <p class="review-text">This product exceeded my expectations. The quality is outstanding and it arrived exactly as described. The packaging was also very professional. I would definitely recommend this to others and will be purchasing again.</p>
                            </div>
                            <div class="review-footer d-flex justify-content-between align-items-center mt-3">
                                <div class="review-helpful">
                                    <span class="text-muted me-2">Was this helpful?</span>
                                    <button class="btn btn-link btn-sm p-0 me-2">
                                        <i class="bi bi-hand-thumbs-up"></i> Yes (12)
                                    </button>
                                    <button class="btn btn-link btn-sm p-0">
                                        <i class="bi bi-hand-thumbs-down"></i> No (0)
                                    </button>
                                </div>
                                <span class="verified-purchase badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Verified Purchase
                                </span>
                            </div>
                        </div>

                        <hr>

                        <!-- Review Item 2 -->
                        <div class="review-item">
                            <div class="review-header d-flex justify-content-between align-items-start">
                                <div class="reviewer-info d-flex align-items-center">
                                    <div class="reviewer-avatar">
                                        <i class="bi bi-person-circle text-primary"></i>
                                    </div>
                                    <div class="reviewer-details ms-3">
                                        <h5 class="reviewer-name mb-1">Mike Chen</h5>
                                        <div class="review-meta d-flex align-items-center">
                                            <div class="review-rating me-2">
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star-fill text-warning"></i>
                                                <i class="bi bi-star text-muted"></i>
                                            </div>
                                            <span class="review-date text-muted">December 10, 2024</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="review-actions">
                                    <button class="btn btn-link btn-sm text-muted">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="review-content mt-3">
                                <h6 class="review-title">Good Product, Fast Shipping</h6>
                                <p class="review-text">Overall satisfied with this purchase. The product quality is good and shipping was faster than expected. Only minor issue was the packaging could be improved, but the product itself is exactly what I ordered.</p>
                            </div>
                            <div class="review-footer d-flex justify-content-between align-items-center mt-3">
                                <div class="review-helpful">
                                    <span class="text-muted me-2">Was this helpful?</span>
                                    <button class="btn btn-link btn-sm p-0 me-2">
                                        <i class="bi bi-hand-thumbs-up"></i> Yes (8)
                                    </button>
                                    <button class="btn btn-link btn-sm p-0">
                                        <i class="bi bi-hand-thumbs-down"></i> No (1)
                                    </button>
                                </div>
                                <span class="verified-purchase badge bg-success">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Verified Purchase
                                </span>
                            </div>
                        </div>

                        <!-- Load More Reviews -->
                        <div class="text-center mt-4">
                            <button class="btn btn-outline-primary">
                                <i class="bi bi-arrow-down-circle me-1"></i>
                                Load More Reviews
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Write Review Section -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-primary mb-4 d-flex align-items-center">
                    <i class="bi bi-pencil-square me-2"></i>
                    Write a Review
                </h2>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Share Your Experience</h3>
                    </div>
                    <div class="card-body">
                        <form class="review-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Your Rating *</label>
                                        <div class="product-rating-input" data-rating="0">
                                            <i class="bi bi-star rating-star" data-value="1"></i>
                                            <i class="bi bi-star rating-star" data-value="2"></i>
                                            <i class="bi bi-star rating-star" data-value="3"></i>
                                            <i class="bi bi-star rating-star" data-value="4"></i>
                                            <i class="bi bi-star rating-star" data-value="5"></i>
                                            <span class="rating-text ms-2">Click to rate</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reviewTitle" class="form-label">Review Title *</label>
                                        <input type="text" class="form-control" id="reviewTitle" placeholder="Summarize your experience" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="reviewContent" class="form-label">Your Review *</label>
                                <textarea class="form-control" id="reviewContent" rows="4" placeholder="Share details about your experience with this product..." required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Product Quality</label>
                                        <div class="product-rating-input rating-small" data-rating="0">
                                            <i class="bi bi-star rating-star" data-value="1"></i>
                                            <i class="bi bi-star rating-star" data-value="2"></i>
                                            <i class="bi bi-star rating-star" data-value="3"></i>
                                            <i class="bi bi-star rating-star" data-value="4"></i>
                                            <i class="bi bi-star rating-star" data-value="5"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Value for Money</label>
                                        <div class="product-rating-input rating-small" data-rating="0">
                                            <i class="bi bi-star rating-star" data-value="1"></i>
                                            <i class="bi bi-star rating-star" data-value="2"></i>
                                            <i class="bi bi-star rating-star" data-value="3"></i>
                                            <i class="bi bi-star rating-star" data-value="4"></i>
                                            <i class="bi bi-star rating-star" data-value="5"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="recommendProduct">
                                    <label class="form-check-label" for="recommendProduct">
                                        <i class="bi bi-heart me-1"></i>
                                        I would recommend this product to others
                                    </label>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary me-2">
                                    <i class="bi bi-x-circle me-1"></i>
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Submit Review
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Product Rating JavaScript -->
    <script>
        // Interactive Star Rating Functionality
        document.querySelectorAll('.product-rating-input').forEach(ratingContainer => {
            const stars = ratingContainer.querySelectorAll('.rating-star');
            const ratingText = ratingContainer.querySelector('.rating-text');
            
            stars.forEach((star, index) => {
                star.addEventListener('click', () => {
                    const value = index + 1;
                    ratingContainer.dataset.rating = value;
                    
                    // Update star display
                    stars.forEach((s, i) => {
                        if (i < value) {
                            s.className = 'bi bi-star-fill rating-star text-warning';
                        } else {
                            s.className = 'bi bi-star rating-star';
                        }
                    });
                    
                    // Update rating text
                    if (ratingText) {
                        const ratingTexts = ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                        ratingText.textContent = ratingTexts[value - 1] + ` (${value} star${value !== 1 ? 's' : ''})`;
                    }
                });
                
                // Hover effect
                star.addEventListener('mouseenter', () => {
                    stars.forEach((s, i) => {
                        if (i <= index) {
                            s.classList.add('text-warning');
                        } else {
                            s.classList.remove('text-warning');
                        }
                    });
                });
            });
            
            // Reset hover effect when leaving rating container
            ratingContainer.addEventListener('mouseleave', () => {
                const currentRating = parseInt(ratingContainer.dataset.rating);
                stars.forEach((s, i) => {
                    if (i < currentRating) {
                        s.className = 'bi bi-star-fill rating-star text-warning';
                    } else {
                        s.className = 'bi bi-star rating-star';
                        s.classList.remove('text-warning');
                    }
                });
            });
        });

        // Form submission
        document.querySelector('.review-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic validation
            const rating = this.querySelector('.product-rating-input').dataset.rating;
            const title = this.querySelector('#reviewTitle').value;
            const content = this.querySelector('#reviewContent').value;
            
            if (!rating || rating === '0') {
                alert('Please provide a rating for this product.');
                return;
            }
            
            if (!title.trim()) {
                alert('Please provide a review title.');
                return;
            }
            
            if (!content.trim()) {
                alert('Please write your review.');
                return;
            }
            
            // Simulate form submission
            alert('Thank you for your review! It will be published after moderation.');
            this.reset();
            
            // Reset rating displays
            this.querySelectorAll('.product-rating-input').forEach(container => {
                container.dataset.rating = '0';
                container.querySelectorAll('.rating-star').forEach(star => {
                    star.className = 'bi bi-star rating-star';
                });
                const ratingText = container.querySelector('.rating-text');
                if (ratingText) {
                    ratingText.textContent = 'Click to rate';
                }
            });
        });
    </script>
</body>
</html> 