# AI Guidelines: CozyWish Design System

**For AI agents working with Django templates using CozyWish design system.**

## Core Principles

- Use `cozywish.css` as single import
- Follow Bootstrap conventions with CozyWish extensions
- Prefix custom utilities with `cw-`
- Always include responsive classes

## Django Template Integration

### Base Template Setup
```html
<!-- base.html -->
<link rel="stylesheet" href="{% static 'css/cozywish.css' %}">
```

### Component Usage Patterns

#### Buttons
```html
<button class="btn btn-primary">Primary</button>
<button class="btn btn-secondary">Secondary</button>
<a href="#" class="btn btn-outline-primary">Link Button</a>
```

#### Forms
```html
<div class="mb-3">
  <label class="form-label">{{ field.label }}</label>
  {{ field|add_class:"form-control" }}
</div>
```

#### Alerts
```html
{% if messages %}
  {% for message in messages %}
    <div class="alert alert-{{ message.tags }}">{{ message }}</div>
  {% endfor %}
{% endif %}
```

#### Cards
```html
<div class="card">
  <div class="card-header">Title</div>
  <div class="card-body">Content</div>
</div>
```

#### Navigation
```html
<nav class="navbar navbar-expand-lg">
  <div class="container">
    <a class="navbar-brand" href="/">CozyWish</a>
  </div>
</nav>
```

## Class Naming Conventions

- **Bootstrap**: Standard Bootstrap classes (no prefix)
- **Custom utilities**: `cw-` prefix (e.g., `cw-grid`, `cw-section`)
- **Components**: Follow component CSS files in `/css/` folder

## Common Patterns

### Django Forms with Design System
```html
<form method="post">
  {% csrf_token %}
  {% for field in form %}
    <div class="mb-3">
      <label class="form-label">{{ field.label }}</label>
      {{ field|add_class:"form-control" }}
      {% if field.errors %}
        <div class="text-danger">{{ field.errors.0 }}</div>
      {% endif %}
    </div>
  {% endfor %}
  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### Loading States
```html
<div class="cw-loading" id="loading">
  <div class="spinner-border text-primary"></div>
</div>
```

### Tables
```html
<div class="table-responsive">
  <table class="table table-striped">
    <thead><tr><th>Header</th></tr></thead>
    <tbody>
      {% for item in items %}
        <tr><td>{{ item.name }}</td></tr>
      {% endfor %}
    </tbody>
  </table>
</div>
```

## Key Rules for AI Agents

1. **Always use responsive classes**: `container`, `row`, `col-*`
2. **Include Bootstrap utilities**: `mb-3`, `text-center`, etc.
3. **Use semantic HTML**: proper heading hierarchy, form labels
4. **Add CSRF tokens**: `{% csrf_token %}` in forms
5. **Handle Django messages**: Include alert display for user feedback
6. **Use static file references**: `{% static 'css/cozywish.css' %}`

## Component Reference

- Check `/components/` folder for HTML examples
- Each component has corresponding CSS in `/css/` folder
- Use `cozywish.css` for complete system import

## Responsive Design

- Mobile-first approach
- Use Bootstrap grid system
- Test across device sizes
- Include viewport meta tag
