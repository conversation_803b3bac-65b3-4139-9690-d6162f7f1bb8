# Quick Reference: CozyWish Design System

## Colors

### Brand Colors
- `--cw-color-primary: #43251B` (<PERSON>)
- `--cw-color-secondary: #5A342A` (<PERSON> Brown)  
- `--cw-color-accent: #FEF6F0` (Light Cream)

### Semantic Colors
- `text-success` / `bg-success` - Green
- `text-danger` / `bg-danger` - Red
- `text-warning` / `bg-warning` - Yellow
- `text-info` / `bg-info` - Blue

## Common Components

### Buttons
```html
<button class="btn btn-primary">Primary</button>
<button class="btn btn-outline-primary">Outline</button>
<button class="btn btn-sm">Small</button>
<button class="btn btn-lg">Large</button>
```

### Alerts
```html
<div class="alert alert-success">Success message</div>
<div class="alert alert-danger">Error message</div>
<div class="alert alert-warning">Warning message</div>
```

### Cards
```html
<div class="card">
  <div class="card-header">Title</div>
  <div class="card-body">Content</div>
  <div class="card-footer">Footer</div>
</div>
```

### Forms
```html
<div class="mb-3">
  <label class="form-label">Label</label>
  <input type="text" class="form-control">
</div>
<div class="mb-3">
  <select class="form-select">
    <option>Choose...</option>
  </select>
</div>
```

### Tables
```html
<div class="table-responsive">
  <table class="table table-striped">
    <thead><tr><th>Header</th></tr></thead>
    <tbody><tr><td>Data</td></tr></tbody>
  </table>
</div>
```

### Navigation
```html
<nav class="navbar navbar-expand-lg">
  <div class="container">
    <a class="navbar-brand" href="#">Brand</a>
  </div>
</nav>
```

## Layout

### Grid System
```html
<div class="container">
  <div class="row">
    <div class="col-md-6">Half width</div>
    <div class="col-md-6">Half width</div>
  </div>
</div>
```

### Custom Grid (CozyWish)
```html
<div class="cw-grid cw-grid-2">Grid 2 columns</div>
<div class="cw-grid cw-grid-3">Grid 3 columns</div>
<div class="cw-grid cw-grid-4">Grid 4 columns</div>
```

## Spacing

### Margin/Padding Classes
- `m-{size}` / `p-{size}` (all sides)
- `mt-{size}` / `pt-{size}` (top)
- `mb-{size}` / `pb-{size}` (bottom)
- `mx-{size}` / `px-{size}` (horizontal)
- `my-{size}` / `py-{size}` (vertical)

### Sizes: `0`, `1`, `2`, `3`, `4`, `5` (4px, 8px, 12px, 16px, 20px, 24px)

## Typography

### Headings
```html
<h1 class="text-primary">Main Title</h1>
<h2 class="text-secondary">Subtitle</h2>
<p class="text-muted">Muted text</p>
```

### Text Utilities
- `text-center` / `text-start` / `text-end`
- `fw-bold` / `fw-normal` / `fw-light`
- `fs-1` to `fs-6` (font sizes)

## Utilities

### Display
- `d-none` / `d-block` / `d-inline` / `d-flex`
- `d-md-block` (responsive display)

### Flexbox
- `d-flex justify-content-center align-items-center`
- `flex-column` / `flex-row`

### Visibility
- `visible` / `invisible`
- `d-none d-md-block` (hide on mobile)

## Django Specific

### Static Files
```html
<link rel="stylesheet" href="{% static 'css/cozywish.css' %}">
```

### Form Integration
```html
{{ field|add_class:"form-control" }}
```

### Messages
```html
{% for message in messages %}
  <div class="alert alert-{{ message.tags }}">{{ message }}</div>
{% endfor %}
```

### Loading States
```html
<div class="spinner-border text-primary"></div>
<div class="placeholder-glow">
  <span class="placeholder col-6"></span>
</div>
```

## Custom Classes (cw- prefix)

- `cw-container` - Custom container
- `cw-section` - Section spacing
- `cw-grid` - Custom grid system
- `cw-flex-center` - Flex center alignment
- `cw-loading` - Loading component wrapper
