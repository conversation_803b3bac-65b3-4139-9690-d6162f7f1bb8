<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Signup - CozyWish</title>
    
    <!-- Bootstrap 5 CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CozyWish Design System -->
    <link rel="stylesheet" href="../cozywish.css">
    
    <!-- Custom styles to remove hover effects -->
    <style>
        /* Remove hover effects from form elements */
        .password-toggle:hover {
            color: var(--bs-primary) !important;
            background-color: #fff !important;
            border-color: var(--bs-border-color) !important;
            transform: none !important;
        }
        
        .password-toggle:active {
            transform: none !important;
            background-color: #fff !important;
        }
        
        /* Remove any hover transforms or animations */
        .btn:hover,
        .form-control:hover,
        .input-group-text:hover,
        .card:hover {
            transform: none !important;
        }
        
        /* Remove hover effects from links */
        a:hover {
            transform: none !important;
        }
        
        /* Ensure navbar doesn't interfere with form layout */
        .signup-main-content {
            padding-top: 100px;
            min-height: 100vh;
        }

        /* Custom signup logo styling */
        .signup-logo-circle {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }

        .signup-logo-circle i {
            font-size: 2rem;
        }

        /* Enhanced form styling to match design reference */
        .form-control {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            border-color: #8B4513;
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .input-group-text {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-right: none;
            color: #666;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #A0522D, #8B4513);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
        }
    </style>
</head>

<body class="bg-light">
    <!-- Guest Navbar -->
    <nav class="guest-navbar fixed-top">
        <div class="container">
            <div class="row align-items-center">
                <!-- Brand Logo -->
                <div class="col-auto">
                    <div class="guest-navbar-brand">
                        <a href="/" class="guest-navbar-logo">
                            <img src="../assets/logo/logo-light.svg" alt="CozyWish Logo">
                        </a>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="col-auto ms-auto">
                    <div class="guest-navbar-actions">
                        <!-- For Business Button -->
                        <a href="/business/signup/" class="btn-for-business me-3">
                            <i class="bi bi-building"></i>
                            <span>For Business</span>
                        </a>

                        <!-- Menu Dropdown -->
                        <div class="dropdown">
                            <button class="btn-menu dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person"></i>
                                <span>Menu</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/customer/login/">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Log in
                                </a></li>
                                <li><a class="dropdown-item" href="/customer/signup/">
                                    <i class="bi bi-person-plus me-2"></i>
                                    Sign Up
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/business/login/">
                                    <i class="bi bi-building-gear me-2"></i>
                                    Business Login
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content with top padding for fixed navbar -->
    <div class="container signup-main-content">
        <div class="row justify-content-center align-items-center py-5">
            <div class="col-12 col-md-8 col-lg-6 col-xl-5">
                <!-- Signup Card -->
                <div class="card shadow-lg border-0">
                    <div class="card-body p-4 p-md-5">
                        <!-- Logo and Header -->
                        <div class="text-center mb-4">
                            <div class="signup-logo-container mb-3">
                                <div class="signup-logo-circle">
                                    <i class="bi bi-person-plus-fill text-white"></i>
                                </div>
                            </div>
                            <h1 class="h3 fw-bold mb-2" style="color: #2c3e50;">Join CozyWish</h1>
                            <p class="text-muted">Create your account</p>
                        </div>

                        <!-- Signup Form -->
                        <form id="signupForm" novalidate>
                            <!-- Email Field -->
                            <div class="form-group mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email"
                                           placeholder="Enter your email address"
                                           required>
                                </div>
                                <div class="form-text text-muted small">
                                    We'll never share your email with anyone else.
                                </div>
                                <div class="invalid-feedback">
                                    Please provide a valid email address.
                                </div>
                                <div class="valid-feedback">
                                    Email looks good!
                                </div>
                            </div>

                            <!-- Password Field -->
                            <div class="form-group mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password"
                                           placeholder="Create a strong password"
                                           required>
                                    <button class="btn btn-outline-secondary password-toggle" 
                                            type="button" 
                                            onclick="togglePassword('password')"
                                            tabindex="-1">
                                        <i class="bi bi-eye" id="passwordToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="form-text text-muted small">
                                    Your password must contain at least 8 characters, cannot be entirely numeric, and should not be too common.
                                </div>
                                <div class="invalid-feedback">
                                    Password must meet all requirements above.
                                </div>
                                <div class="valid-feedback">
                                    Password meets all requirements!
                                </div>
                                <!-- Password Strength Indicator -->
                                <div class="mt-2">
                                    <div class="progress" style="height: 4px;">
                                        <div class="progress-bar" id="passwordStrengthBar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small id="passwordStrength" class="form-text"></small>
                                </div>
                            </div>

                            <!-- Confirm Password Field -->
                            <div class="form-group mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control" 
                                           id="confirmPassword" 
                                           name="confirmPassword"
                                           placeholder="Confirm your password"
                                           required>
                                    <button class="btn btn-outline-secondary password-toggle" 
                                            type="button" 
                                            onclick="togglePassword('confirmPassword')"
                                            tabindex="-1">
                                        <i class="bi bi-eye" id="confirmPasswordToggleIcon"></i>
                                    </button>
                                </div>
                                <div class="form-text text-muted small">
                                    Enter the same password as before, for verification.
                                </div>
                                <div class="invalid-feedback">
                                    Passwords do not match.
                                </div>
                                <div class="valid-feedback">
                                    Passwords match!
                                </div>
                            </div>

                            <!-- Terms Agreement -->
                            <div class="form-group mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="termsAgreement" 
                                           name="termsAgreement"
                                           required>
                                    <label class="form-check-label" for="termsAgreement">
                                        I agree to the <a href="#" class="text-primary text-decoration-none">Terms of Service</a> 
                                        and <a href="#" class="text-primary text-decoration-none">Privacy Policy</a> <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">
                                        You must agree to the terms and conditions.
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="bi bi-person-plus me-2"></i>
                                    Create Account
                                </button>
                            </div>

                            <!-- Alternative Links -->
                            <div class="text-center">
                                <p class="text-muted mb-3">
                                    Already have an account?
                                    <a href="/customer/login/" class="text-decoration-none" style="color: #8B4513; font-weight: 600;">
                                        Sign In
                                    </a>
                                </p>
                                <p class="text-muted small">
                                    Looking to list your business?
                                    <a href="/business/signup/" class="text-decoration-none" style="color: #8B4513;">
                                        Business Signup
                                    </a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-4">
                    <p class="text-muted small">
                        &copy; 2024 CozyWish. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Custom JavaScript -->
    <script>
        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + 'ToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Password strength validation
        function validatePasswordStrength(password) {
            const requirements = [
                { regex: /.{8,}/, message: 'At least 8 characters' },
                { regex: /[A-Z]/, message: 'One uppercase letter' },
                { regex: /[a-z]/, message: 'One lowercase letter' },
                { regex: /\d/, message: 'One number' },
                { regex: /[!@#$%^&*(),.?":{}|<>]/, message: 'One special character' }
            ];

            let score = 0;
            requirements.forEach(req => {
                if (req.regex.test(password)) score++;
            });

            return { score, total: requirements.length, requirements };
        }

        // Email validation
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Real-time validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('signupForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const termsCheckbox = document.getElementById('termsAgreement');
            const passwordStrengthBar = document.getElementById('passwordStrengthBar');
            const passwordStrengthText = document.getElementById('passwordStrength');

            // Email validation
            emailInput.addEventListener('input', function() {
                const email = this.value;
                if (email.length > 0) {
                    if (validateEmail(email)) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Password strength validation
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const validation = validatePasswordStrength(password);
                
                // Update progress bar
                const percentage = (validation.score / validation.total) * 100;
                passwordStrengthBar.style.width = percentage + '%';
                
                // Update progress bar color and text
                if (validation.score === 0) {
                    passwordStrengthBar.className = 'progress-bar';
                    passwordStrengthText.textContent = '';
                    passwordStrengthText.className = 'form-text';
                } else if (validation.score < 2) {
                    passwordStrengthBar.className = 'progress-bar bg-danger';
                    passwordStrengthText.textContent = 'Weak password';
                    passwordStrengthText.className = 'form-text text-danger';
                } else if (validation.score < 4) {
                    passwordStrengthBar.className = 'progress-bar bg-warning';
                    passwordStrengthText.textContent = 'Medium password';
                    passwordStrengthText.className = 'form-text text-warning';
                } else {
                    passwordStrengthBar.className = 'progress-bar bg-success';
                    passwordStrengthText.textContent = 'Strong password';
                    passwordStrengthText.className = 'form-text text-success';
                }

                // Validate password field
                if (password.length > 0) {
                    if (validation.score === validation.total) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }

                // Re-validate confirm password
                if (confirmPasswordInput.value.length > 0) {
                    confirmPasswordInput.dispatchEvent(new Event('input'));
                }
            });

            // Confirm password validation
            confirmPasswordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirmPassword = this.value;

                if (confirmPassword.length > 0) {
                    if (password === confirmPassword && password.length > 0) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                } else {
                    this.classList.remove('is-valid', 'is-invalid');
                }
            });

            // Terms checkbox validation
            termsCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Validate all fields
                const email = emailInput.value;
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                const termsAccepted = termsCheckbox.checked;

                let isValid = true;

                // Email validation
                if (!validateEmail(email)) {
                    emailInput.classList.add('is-invalid');
                    isValid = false;
                }

                // Password validation
                const passwordValidation = validatePasswordStrength(password);
                if (passwordValidation.score < passwordValidation.total) {
                    passwordInput.classList.add('is-invalid');
                    isValid = false;
                }

                // Confirm password validation
                if (password !== confirmPassword) {
                    confirmPasswordInput.classList.add('is-invalid');
                    isValid = false;
                }

                // Terms validation
                if (!termsAccepted) {
                    termsCheckbox.classList.add('is-invalid');
                    isValid = false;
                }

                if (isValid) {
                    // Simulate form submission
                    const submitBtn = document.getElementById('submitBtn');
                    submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Creating Account...';
                    submitBtn.disabled = true;

                    // Simulate API call
                    setTimeout(() => {
                        alert('Account created successfully! Welcome to CozyWish!');
                        submitBtn.innerHTML = '<i class="bi bi-person-plus me-2"></i>Create Account';
                        submitBtn.disabled = false;
                        form.reset();
                        // Remove validation classes
                        form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                            el.classList.remove('is-valid', 'is-invalid');
                        });
                        passwordStrengthBar.style.width = '0%';
                        passwordStrengthText.textContent = '';
                    }, 2000);
                } else {
                    // Show validation summary
                    const firstInvalidField = form.querySelector('.is-invalid');
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                    }
                }
            });
        });
    </script>
</body>
</html>
