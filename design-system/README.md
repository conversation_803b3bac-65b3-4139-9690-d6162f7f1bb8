# CozyWish Design System

A comprehensive CSS design system for Django web applications.

## Quick Start

### 1. Include CSS in Django Templates

```html
<!-- In your base template -->
<link rel="stylesheet" href="{% static 'css/cozywish.css' %}">
```

### 2. Basic Usage

```html
<!-- Button -->
<button class="btn btn-primary">Primary Action</button>

<!-- Alert -->
<div class="alert alert-success">Success message</div>

<!-- Card -->
<div class="card">
  <div class="card-body">Content here</div>
</div>
```

## Component Structure

- **Foundation**: `css/tokens.css` - Design tokens (colors, spacing, typography)
- **Components**: Individual CSS files for each component
- **Examples**: HTML files showing component usage

## Core CSS Classes

- Prefix: `cw-` for custom utilities
- Bootstrap classes work as normal
- Custom components extend Bootstrap

## File Organization
